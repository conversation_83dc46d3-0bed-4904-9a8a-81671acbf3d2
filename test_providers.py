#!/usr/bin/env python3
"""
Test script to verify that Gemini and Google embeddings are working correctly.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_embedding_provider():
    """Test Google embeddings provider."""
    print("🧪 Testing Google Embeddings Provider...")
    
    try:
        from app.providers.embeddings import get_embedding_provider
        
        # Get Google embeddings provider
        provider = get_embedding_provider(provider="google")
        
        # Test embedding a simple text
        test_text = "Hello, this is a test for Google embeddings."
        embedding = provider.embed_text(test_text)
        
        print(f"✅ Google embeddings working! Embedding dimension: {len(embedding)}")
        return True
        
    except Exception as e:
        print(f"❌ Google embeddings failed: {e}")
        return False

def test_llm_provider():
    """Test Gemini LLM provider."""
    print("\n🧪 Testing Gemini LLM Provider...")
    
    try:
        from app.providers.llm import get_llm_provider
        
        # Get Gemini LLM provider
        provider = get_llm_provider(provider="gemini", model="gemini-1.5-flash")
        
        # Test a simple chat
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Say hello and confirm you are working correctly."}
        ]
        
        response = provider.chat(messages)
        
        if response.get("content"):
            print(f"✅ Gemini LLM working! Response: {response['content'][:100]}...")
            return True
        else:
            print(f"❌ Gemini LLM failed: No content in response")
            return False
        
    except Exception as e:
        print(f"❌ Gemini LLM failed: {e}")
        return False

def test_base_agent():
    """Test BaseAgent with Gemini and Google providers."""
    print("\n🧪 Testing BaseAgent with Gemini/Google providers...")
    
    try:
        from app.agents.BaseAgent import BaseAgent
        
        # Create agent with test project
        agent = BaseAgent(
            project_id="test-project",
            llm_provider="gemini"
        )
        
        if agent.llm_provider and agent.document_manager:
            print("✅ BaseAgent initialized successfully with Gemini/Google providers!")
            
            # Test a simple reply
            response = agent.reply("Hello, can you confirm you're working with Gemini?")
            print(f"✅ BaseAgent response: {response[:100]}...")
            return True
        else:
            print("❌ BaseAgent initialization failed")
            return False
        
    except Exception as e:
        print(f"❌ BaseAgent test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Kairos Backend with Gemini and Google Embeddings")
    print("=" * 60)
    
    # Check environment
    print(f"LLM Provider: {os.getenv('LLM_PROVIDER', 'Not set')}")
    print(f"Embedding Provider: {os.getenv('EMBEDDING_PROVIDER', 'Not set')}")
    print(f"Google API Key: {'✅ Set' if os.getenv('GOOGLE_API_KEY') else '❌ Not set'}")
    print()
    
    # Run tests
    tests = [
        test_embedding_provider,
        test_llm_provider,
        test_base_agent
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("✅ Kairos backend is working correctly with Gemini and Google embeddings!")
    else:
        print(f"⚠️  Some tests failed. ({passed}/{total} passed)")
        print("Please check the errors above and ensure your configuration is correct.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
