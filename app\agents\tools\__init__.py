from typing import Callable, Dict

# Import individual tool modules and expose a registry
from . import find_files as _find_files
from . import project_search as _project_search
from . import web_search as _web_search
from . import code_execution as _code_execution


def get_tools_registry() -> Dict[str, Callable]:
    """Name -> callable(agent, **kwargs) registry."""
    return {
        "find_files": _find_files.run,
        "project_search": _project_search.run,
        "web_search": _web_search.run,
        "code_execution": _code_execution.run,
    }

