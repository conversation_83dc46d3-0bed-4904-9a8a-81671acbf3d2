"""
Modern tools manager using FastMCP for MCP integration.
Combines built-in tools with FastMCP-managed tools.
"""

import asyncio
from typing import List, Dict, Any
from .tools_builtin import get_builtin_tools
from ..utils.fastmcp_client import get_mcp_tools, add_mcp_server


async def get_all_tools(project_id: str = None) -> List[Dict[str, Any]]:
    """Get all tools: built-in + MCP tools from FastMCP configuration."""
    # Start with built-in tools
    tools = get_builtin_tools()

    # Add MCP tools from FastMCP configuration
    try:
        mcp_tools = await get_mcp_tools(project_id)
        tools.extend(mcp_tools)
        print(f"🔧 Total tools loaded: {len(tools)} ({len(tools) - len(mcp_tools)} built-in + {len(mcp_tools)} MCP)")
    except Exception as e:
        print(f"⚠️ Error loading MCP tools: {e}")

    return tools


def get_all_tools_sync(project_id: str = None) -> List[Dict[str, Any]]:
    """Synchronous wrapper for get_all_tools() - works in async contexts."""
    import concurrent.futures

    def run_async_in_thread():
        """Run the async function in a separate thread with its own event loop."""
        return asyncio.run(get_all_tools(project_id))

    try:
        # Always run in a separate thread to avoid event loop conflicts
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(run_async_in_thread)
            result = future.result(timeout=10)  # 10 second timeout
            return result
    except Exception as e:
        print(f"❌ Error loading tools: {e}")
        return get_builtin_tools()


def add_mcp_server_config(name: str, transport: str, **kwargs) -> bool:
    """Add a new MCP server to the configuration."""
    server_config = {
        "transport": transport,
        **kwargs
    }

    return add_mcp_server(name, server_config)
