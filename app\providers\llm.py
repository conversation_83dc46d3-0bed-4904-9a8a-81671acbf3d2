"""Plug-and-play LLM providers."""

import os
import json
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod

class LLMProvider(ABC):
    """Base LLM provider interface."""
    
    @abstractmethod
    def chat(self, messages: List[Dict], tools: List[Dict] = None, **kwargs) -> Dict:
        pass

class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider."""
    
    def __init__(self, model: str = "gpt-4o-mini"):
        from openai import OpenAI
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.model = model
    
    def chat(self, messages: List[Dict], tools: List[Dict] = None, **kwargs) -> Dict:
        params = {
            "model": self.model,
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", 1000),
            "temperature": kwargs.get("temperature", 0.7)
        }
        
        if tools:
            params["tools"] = tools
            params["tool_choice"] = kwargs.get("tool_choice", "auto")
        
        response = self.client.chat.completions.create(**params)
        
        return {
            "content": response.choices[0].message.content,
            "tool_calls": response.choices[0].message.tool_calls,
            "usage": response.usage.dict() if response.usage else None
        }

class GeminiProvider(LLMProvider):
    """Google Gemini LLM provider using OpenAI-compatible API."""

    def __init__(self, model: str = "gemini-1.5-flash"):
        from openai import OpenAI
        # Use Gemini's OpenAI-compatible endpoint
        self.client = OpenAI(
            api_key=os.getenv("GOOGLE_API_KEY"),
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )
        self.model = model
        print (f"GeminiProvider initialized with model: {self.model} and api_key: {os.getenv('GOOGLE_API_KEY')}")

    def chat(self, messages: List[Dict], tools: List[Dict] = None, **kwargs) -> Dict:
        """Chat using OpenAI-compatible interface."""

        # Filter out messages with null/empty content
        filtered_messages = []
        for msg in messages:
            if msg.get("content") is not None and msg.get("content") != "":
                filtered_messages.append(msg)
            elif msg.get("role") == "assistant" and msg.get("tool_calls"):
                # Keep assistant messages with tool calls even if content is empty
                filtered_messages.append({
                    "role": "assistant",
                    "content": msg.get("content") or "I'll use tools to help answer your question.",
                    "tool_calls": msg.get("tool_calls")
                })
            elif msg.get("role") == "tool":
                # Keep tool messages but ensure content is not null
                filtered_messages.append({
                    "role": "tool",
                    "tool_call_id": msg.get("tool_call_id"),
                    "content": msg.get("content") or "Tool executed successfully"
                })

        params = {
            "model": self.model,
            "messages": filtered_messages,
            "max_tokens": kwargs.get("max_tokens", 1000),
            "temperature": kwargs.get("temperature", 0.7)
        }

        if tools:
            params["tools"] = tools
            params["tool_choice"] = kwargs.get("tool_choice", "auto")

        try:
            response = self.client.chat.completions.create(**params)

            return {
                "content": response.choices[0].message.content,
                "tool_calls": response.choices[0].message.tool_calls,
                "usage": response.usage.dict() if response.usage else None
            }
        except Exception as e:
            return {
                "content": f"Error: {str(e)}",
                "tool_calls": None,
                "usage": None
            }

class OfflineLLMProvider(LLMProvider):
    """Offline LLM provider using transformers."""
    
    def __init__(self, model: str = "microsoft/DialoGPT-medium"):
        from transformers import AutoTokenizer, AutoModelForCausalLM
        self.tokenizer = AutoTokenizer.from_pretrained(model)
        self.model = AutoModelForCausalLM.from_pretrained(model)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def chat(self, messages: List[Dict], tools: List[Dict] = None, **kwargs) -> Dict:
        # Simple conversation format
        conversation = ""
        for msg in messages:
            if msg["role"] == "user":
                conversation += f"User: {msg['content']}\n"
            elif msg["role"] == "assistant":
                conversation += f"Assistant: {msg['content']}\n"
        
        conversation += "Assistant: "
        
        inputs = self.tokenizer.encode(conversation, return_tensors="pt")
        outputs = self.model.generate(
            inputs,
            max_length=inputs.shape[1] + kwargs.get("max_tokens", 100),
            temperature=kwargs.get("temperature", 0.7),
            pad_token_id=self.tokenizer.eos_token_id
        )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        assistant_response = response.split("Assistant: ")[-1]
        
        return {
            "content": assistant_response,
            "tool_calls": None,
            "usage": None
        }

def get_llm_provider(provider: str = None, **kwargs) -> LLMProvider:
    """Factory function to get LLM provider."""
    provider = provider or os.getenv("LLM_PROVIDER", "openai")
    
    providers = {
        "openai": OpenAIProvider,
        "gemini": GeminiProvider,
        # "offline": OfflineLLMProvider
    }
    
    if provider not in providers:
        raise ValueError(f"Unknown provider: {provider}. Available: {list(providers.keys())}")
    
    try:
        return providers[provider](**kwargs)
    except Exception as e:
        print(f"Failed to initialize {provider} LLM: {e}")
        # Fallback to offline if available
        if provider != "offline":
            print("model is not available, stopping")
            # return OfflineLLMProvider(**kwargs)
        raise
