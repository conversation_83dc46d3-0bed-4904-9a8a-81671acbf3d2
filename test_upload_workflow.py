#!/usr/bin/env python3
"""Test the upload workflow to verify CSV handling works correctly."""

import os
import pandas as pd
from app.managers.ProjectManager import ProjectManager

def test_upload_workflow():
    """Test CSV upload workflow."""
    print("🧪 Testing Upload Workflow")
    
    # Create test project
    project = ProjectManager(project_name="Upload Test")
    print(f"📁 Project: {project.project_id}")
    
    # Create simple CSV
    data = {'name': ['Alice', 'Bob'], 'age': [25, 30], 'city': ['NY', 'LA']}
    df = pd.DataFrame(data)
    csv_content = df.to_csv(index=False).encode('utf-8')
    
    # Test the upload workflow (simulating the route)
    saved_path = project.save_file("test_data.csv", csv_content, location="input")
    print(f"✅ CSV saved to: {saved_path}")
    
    # Verify file structure
    status = project.get_project_status()
    print(f"📊 Input files: {status['input_files']}")
    
    # Test agent can reference the file
    agent = project.agent_manager.get_agent("base")
    if agent:
        code = '''
import pandas as pd
import os

print("Current directory:", os.getcwd())
print("Files:", os.listdir("."))

if os.path.exists("../input/test_data.csv"):
    df = pd.read_csv("../input/test_data.csv")
    print(f"Loaded CSV: {df.shape}")
    print(df.head())
else:
    print("CSV not found")
'''
        result = agent.code_execution(code)
        print("💻 Code execution result:")
        import json
        print(json.loads(result)['stdout'])
    
    print("✅ Upload workflow working correctly!")

if __name__ == "__main__":
    test_upload_workflow()
