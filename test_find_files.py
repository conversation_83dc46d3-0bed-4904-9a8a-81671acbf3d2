#!/usr/bin/env python3
"""
Test the find_files functionality directly.
"""
import sys
sys.path.append('.')

from app.agents.BaseAgent import BaseAgent

def test_find_files():
    """Test the find_files method directly."""
    print("🧪 Testing find_files functionality...")
    
    try:
        # Create agent with the project that has titanic.csv
        agent = BaseAgent(
            project_id="4191a198-b6df-4a5c-afaf-89d0c11b47e1",
            llm_provider="gemini"
        )
        
        # Test different search patterns
        patterns = [
            "*.csv",
            "titanic",
            "csv",
            "*",
            "*.txt"
        ]
        
        for pattern in patterns:
            print(f"\n🔍 Searching for: '{pattern}'")
            result = agent._find_files(pattern)
            print(result)
            print("-" * 50)
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_find_files()
