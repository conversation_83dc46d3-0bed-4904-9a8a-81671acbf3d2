from typing import Any
import os
import json
import subprocess

def run(agent: Any, code: str, filename: str = None) -> str:
    """
    Execute Python code in a safe environment with proper error handling.

    Args:
        agent: The agent instance (contains work_dir and project info)
        code: Python code to execute
        filename: Optional filename to save the code to

    Returns:
        JSON string with execution results
    """
    try:
        # Ensure work directory exists
        work_dir = getattr(agent, 'work_dir', 'work')
        os.makedirs(work_dir, exist_ok=True)

        # Get project root - use the actual project path if available
        if hasattr(agent, 'project_id') and agent.project_id:
            project_root = os.path.abspath(f"projects/{agent.project_id}")
        else:
            project_root = os.path.abspath('.')

        # Properly indent user code for the try block
        def indent_code(code_str, spaces=4):
            """Indent each line of code by the specified number of spaces."""
            lines = code_str.split('\n')
            indented_lines = []
            for line in lines:
                if line.strip():  # Only indent non-empty lines
                    indented_lines.append(' ' * spaces + line)
                else:
                    indented_lines.append(line)  # Keep empty lines as-is
            return '\n'.join(indented_lines)

        indented_user_code = indent_code(code)

        # Create enhanced code template with better error handling
        project_root_escaped = project_root.replace('\\', '\\\\')
        work_dir_escaped = work_dir.replace('\\', '\\\\')
        project_id = getattr(agent, 'project_id', '')

        template = '''
import os
import sys
import json
import traceback
from pathlib import Path

# Add project root to Python path
project_root = r"{project_root}"
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Change to work directory
work_dir = r"{work_dir}"
if os.path.exists(work_dir):
    os.chdir(work_dir)

# Utility functions for project analysis
def analyze_directory(path='.', max_depth=3):
    """Analyze directory structure."""
    structure = {{}}
    try:
        for root, dirs, files in os.walk(path):
            level = root.replace(path, '').count(os.sep)
            if level >= max_depth:
                dirs[:] = []
                continue
            structure[root] = {{
                'dirs': dirs,
                'files': files,
                'file_count': len(files),
                'dir_count': len(dirs)
            }}
    except Exception as e:
        structure['error'] = str(e)
    return structure

def get_file_info(pattern='*'):
    """Get file information matching pattern."""
    import glob
    try:
        files = glob.glob(pattern, recursive=True)
        return [{{
            'path': f,
            'size': os.path.getsize(f) if os.path.exists(f) else 0,
            'modified': os.path.getmtime(f) if os.path.exists(f) else 0
        }} for f in files]
    except Exception as e:
        return [{{'error': str(e)}}]

def find_project_file(filename):
    """Find a file in the project structure and return the correct path."""
    import os
    from pathlib import Path

    # Common project locations to search
    search_locations = [
        filename,  # Direct path as given
        f"../input/{{filename}}",  # Input directory
        f"../output/{{filename}}",  # Output directory
        f"../{{filename}}",  # Project root
        f"../../{{filename}}",  # Parent directory
    ]

    for location in search_locations:
        if os.path.exists(location):
            return os.path.abspath(location)

    # If not found, return the original filename
    return filename

def load_csv(filename, **kwargs):
    """Load CSV file with pandas, automatically finding it in project structure."""
    try:
        import pandas as pd

        # First try to find the file
        filepath = find_project_file(filename)

        if os.path.exists(filepath):
            print(f"Loading CSV from: {{filepath}}")
            return pd.read_csv(filepath, **kwargs)
        else:
            # Try common locations with better error reporting
            search_locations = [
                filename,
                f"../input/{{filename}}",
                f"../output/{{filename}}",
                f"../{{filename}}",
            ]

            print(f"Searching for {{filename}} in:")
            for location in search_locations:
                abs_path = os.path.abspath(location)
                exists = os.path.exists(location)
                print(f"  {{location}} -> {{abs_path}} (exists: {{exists}})")

            raise FileNotFoundError(f"Could not find {{filename}} in any common locations")
    except ImportError:
        raise ImportError("pandas not available - install with: pip install pandas")

def load_file(filename, mode='r', **kwargs):
    """Load any file with automatic path resolution."""
    filepath = find_project_file(filename)

    if os.path.exists(filepath):
        print(f"Loading file from: {{filepath}}")
        with open(filepath, mode, **kwargs) as f:
            return f.read()
    else:
        raise FileNotFoundError(f"Could not find {{filename}}")

def save_to_output(filename, content, mode='w'):
    """Save content to the output directory."""
    output_dir = "../output"
    os.makedirs(output_dir, exist_ok=True)
    filepath = os.path.join(output_dir, filename)

    with open(filepath, mode) as f:
        if isinstance(content, str):
            f.write(content)
        else:
            # Assume it's a pandas DataFrame or similar
            content.to_csv(f, index=False)

    print(f"Saved to: {{os.path.abspath(filepath)}}")
    return filepath

def list_project_files(directory=".."):
    """List all files in the project structure."""
    import os
    files = []
    for root, dirs, filenames in os.walk(directory):
        for filename in filenames:
            filepath = os.path.join(root, filename)
            rel_path = os.path.relpath(filepath, directory)
            files.append(rel_path)
    return sorted(files)

# Print current environment info
print(f"Working directory: {{os.getcwd()}}")
print(f"Project root: {{project_root}}")
print(f"Python version: {{sys.version.split()[0]}}")

# Show project structure for easy file access
print("\\n📁 PROJECT STRUCTURE:")
try:
    project_files = list_project_files()
    input_files = [f for f in project_files if f.startswith('input/')]
    output_files = [f for f in project_files if f.startswith('output/')]
    other_files = [f for f in project_files if not f.startswith(('input/', 'output/', 'work/', '.git/'))]

    if input_files:
        print(f"📥 INPUT FILES ({{len(input_files)}}):")
        for f in input_files[:5]:
            print(f"   {{f}}")
        if len(input_files) > 5:
            print(f"   ... and {{len(input_files) - 5}} more")

    if output_files:
        print(f"📤 OUTPUT FILES ({{len(output_files)}}):")
        for f in output_files[:5]:
            print(f"   {{f}}")
        if len(output_files) > 5:
            print(f"   ... and {{len(output_files) - 5}} more")

    if other_files:
        print(f"📄 OTHER FILES ({{len(other_files)}}):")
        for f in other_files[:3]:
            print(f"   {{f}}")
        if len(other_files) > 3:
            print(f"   ... and {{len(other_files) - 3}} more")

except Exception as e:
    print(f"Could not list project files: {{e}}")

print("\\n💡 FILE ACCESS HELPERS:")
print("   load_csv('filename.csv') - Load CSV with auto-path resolution")
print("   load_file('filename.txt') - Load any file with auto-path resolution")
print("   save_to_output('result.csv', data) - Save to output directory")
print("   list_project_files() - List all project files")
print("   find_project_file('filename') - Find file and get correct path")
print()

try:
    # User code starts here:
{user_code}

except Exception as e:
    print(f"Error executing user code: {{e}}")
    print(f"Error details:")
    traceback.print_exc()
    sys.exit(1)
'''.format(
            project_root=project_root_escaped,
            work_dir=work_dir_escaped,
            project_id=project_id,
            user_code=indented_user_code
        )

        # Save code to file if requested
        saved_file = None
        if filename:
            saved_file = os.path.join(work_dir, filename)
            with open(saved_file, "w", encoding='utf-8') as f:
                f.write(template)

        # Execute the code with proper encoding handling
        print(f"Executing code in directory: {work_dir}")

        # Set up environment with proper encoding
        env = os.environ.copy()
        env.update({
            'PYTHONIOENCODING': 'utf-8',
            'PYTHONLEGACYWINDOWSSTDIO': '0',  # Force UTF-8 on Windows
        })

        try:
            result = subprocess.run(
                ["python", "-c", template],
                cwd=work_dir,
                capture_output=True,
                text=True,
                timeout=60,
                env=env,
                encoding='utf-8',
                errors='replace'  # Replace problematic characters instead of failing
            )
        except UnicodeDecodeError:
            # Fallback: try with bytes and manual decoding
            result = subprocess.run(
                ["python", "-c", template],
                cwd=work_dir,
                capture_output=True,
                timeout=60,
                env=env
            )
            # Manually decode with error handling
            try:
                stdout = result.stdout.decode('utf-8', errors='replace') if result.stdout else ""
                stderr = result.stderr.decode('utf-8', errors='replace') if result.stderr else ""
            except AttributeError:
                # Already strings
                stdout = result.stdout or ""
                stderr = result.stderr or ""

            # Create a result-like object
            class FallbackResult:
                def __init__(self, returncode, stdout, stderr):
                    self.returncode = returncode
                    self.stdout = stdout
                    self.stderr = stderr

            result = FallbackResult(result.returncode, stdout, stderr)

        # Clean output format - focus on what matters
        if result.returncode == 0:
            # Success: just return the output, clean and simple
            if result.stdout and result.stdout.strip():
                return result.stdout.strip()
            elif result.stderr and result.stderr.strip():
                # Sometimes useful output goes to stderr even on success
                return f"⚠️ {result.stderr.strip()}"
            else:
                return "✅ Code executed successfully (no output)"
        else:
            # Error: provide detailed information for debugging
            output = {
                "execution_successful": False,
                "error": result.stderr.strip() if result.stderr else "Unknown error",
                "output": result.stdout.strip() if result.stdout else "",
                "code_preview": code[:200] + ("..." if len(code) > 200 else "")
            }
            return json.dumps(output, indent=2, ensure_ascii=False)

    except subprocess.TimeoutExpired:
        return json.dumps({
            "error": "Code execution timed out (60 seconds limit)",
            "code_preview": code[:200] + ("..." if len(code) > 200 else ""),
            "execution_successful": False
        }, indent=2)

    except Exception as e:
        import traceback
        return json.dumps({
            "error": f"Code execution failed: {str(e)}",
            "code_preview": code[:200] + ("..." if len(code) > 200 else ""),
            "traceback": traceback.format_exc(),
            "execution_successful": False
        }, indent=2)

