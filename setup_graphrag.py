#!/usr/bin/env python3
"""
Setup script for Graph-RAG functionality in Kairos backend.
This script helps install dependencies and set up the database.
"""

import subprocess
import sys
import os
from pathlib import Path
import dotenv

# Load environment variables from .env file
dotenv.load_dotenv()

def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr.strip()}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    # Install main dependencies
    if not run_command("pip install -r requirements.txt", "Installing main dependencies"):
        return False
    
    # Install spaCy language model
    if not run_command("python -m spacy download en_core_web_sm", "Installing spaCy English model"):
        print("⚠️  Warning: spaCy model installation failed. You may need to install it manually.")
    
    return True

def setup_environment():
    """Set up environment configuration."""
    print("\n⚙️  Setting up environment configuration...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        # Copy example to .env
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✅ Created .env file from .env.example")
        print("⚠️  Please edit .env file with your actual configuration values")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example file found")
    
    return True

def check_postgresql():
    """Check if PostgreSQL is available."""
    print("\n🐘 Checking PostgreSQL availability...")
    
    # Try to connect to PostgreSQL
    try:
        import psycopg2
        
        # Get config from environment or defaults
        config = {
            "host": os.getenv("POSTGRES_HOST"),
            "port": os.getenv("POSTGRES_PORT"),
            "user": os.getenv("POSTGRES_USER"),
            "password": os.getenv("POSTGRES_PASSWORD", None),
            
        }
        
        conn = psycopg2.connect(**config)
        conn.close()
        print("✅ PostgreSQL connection successful")
        return True
        
    except ImportError:
        print("❌ psycopg2 not installed")
        return False
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        print("⚠️  Please ensure PostgreSQL is running and accessible")
        print(f"   Connection details: {config['host']}:{config['port']}")
        return False

def initialize_database():
    """Initialize the Graph-RAG database."""
    print("\n🗄️  Initializing Graph-RAG database...")
    
    try:
        # Run the database initialization script
        from app.db.init_graphrag_db import main as init_db
        init_db()
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def verify_setup():
    """Verify the Graph-RAG setup."""
    print("\n🔍 Verifying Graph-RAG setup...")
    
    try:
        # Try to import key components
        from app.managers.DocumentManager import DocumentManager
        from app.agents.GraphRAGAgent import GraphRAGAgent
        print("✅ Graph-RAG components imported successfully")
        
        # Try to create a DocumentManager instance
        doc_manager = DocumentManager()
        print("✅ DocumentManager instance created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Setup verification failed: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Graph-RAG for Kairos backend...")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Set up environment
    setup_environment()
    
    # # Install dependencies
    # if not install_dependencies():
    #     print("\n❌ Dependency installation failed. Please check the errors above.")
    #     sys.exit(1)
    
    # Check PostgreSQL
    postgres_ok = check_postgresql()
    
    if postgres_ok:
        # Initialize database
        if not initialize_database():
            print("\n❌ Database initialization failed. Please check the errors above.")
            sys.exit(1)
    else:
        print("\n⚠️  Skipping database initialization due to PostgreSQL connection issues")
    
    # Verify setup
    if not verify_setup():
        print("\n❌ Setup verification failed. Please check the errors above.")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Graph-RAG setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your actual configuration values")
    print("2. Ensure PostgreSQL is running and accessible")
    print("3. Set your OpenAI API key in the .env file")
    print("4. Start the application: python -m uvicorn app.main:app --reload")
    print("\nAPI endpoints available:")
    print("- POST /api/projects/{project_id}/documents/upload")
    print("- POST /api/projects/{project_id}/search/vector")
    print("- POST /api/projects/{project_id}/search/knowledge-graph")
    print("- POST /api/projects/{project_id}/search/hybrid")
    print("- GET  /api/projects/{project_id}/graphrag/stats")

if __name__ == "__main__":
    main()
