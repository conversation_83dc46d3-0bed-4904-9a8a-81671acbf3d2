import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
import uuid

class ConversationManager:
    def __init__(self, conversation_file: Path):
        self.conversation_file = conversation_file
        self.messages: List[Dict] = []
        self._saved_count = 0
        self._commit_hash_updated_count = 0
        self._load_conversation()
        

    def add_message(self, role: str, content: str, commit_hash: str = None) -> dict:
        message = {
            "id": str(uuid.uuid4()),
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
        }
        if commit_hash:
            message["commit_hash"] = commit_hash
        self.messages.append(message)
        return message

    def edit_message(self, message_index: int, new_content: str) -> bool:
        if message_index >= len(self.messages):
            return False
        self.messages[message_index]["content"] = new_content
        self.messages[message_index]["edited_at"] = datetime.now().isoformat()
        self._save_conversation()
        return True

    def get_conversation(self) -> List[Dict]:
        return self.messages.copy()

    def _save_conversation(self):
        # Append only new messages to the file (efficient)
        try:
            # Create parent directory if it doesn't exist
            self.conversation_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Append only new messages as individual JSON lines
            with self.conversation_file.open("a", encoding="utf-8") as f:
                for msg in self.messages[self._saved_count:]:
                    # Ensure each message is on its own line with no extra data
                    json_line = json.dumps(msg, ensure_ascii=False, separators=(',', ':'))
                    f.write(json_line + "\n")
                    
            self._saved_count = len(self.messages)
            
        except Exception as e:
            print(f"Error saving conversation: {e}")

    def _update_message_commit_hash(self,commit_hash: str = None):
        for msg in self.messages[self._commit_hash_updated_count:]:
            msg["commit_hash"] = commit_hash
        self._commit_hash_updated_count = len(self.messages)

    def _load_conversation(self):
        self.messages = []
        if self.conversation_file.exists():
            with self.conversation_file.open("r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        # Parse each line as a single JSON object
                        message = json.loads(line)
                        self.messages.append(message)
                    except json.JSONDecodeError as e:
                        print(f"Skipping malformed JSON on line {line_num}: {str(e)[:100]}")
                        continue
                    except Exception as e:
                        print(f"Error processing line {line_num}: {e}")
                        continue
                        
        self._commit_hash_updated_count = len(self.messages)
        self._saved_count = len(self.messages) 
        print(f"ConversationManager initialized with {len(self.messages)} messages")
