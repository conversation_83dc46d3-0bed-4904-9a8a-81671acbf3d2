"""
Production-ready reasoning engine with timeout handling, circuit breakers, and robust error recovery.
Based on industry best practices for LLM agent orchestration.
"""

import asyncio
import time
import json
from typing import Dict, List, Any, AsyncGenerator, Optional
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ReasoningState(Enum):
    STARTING = "starting"
    THINKING = "thinking"
    TOOL_CALLING = "tool_calling"
    ANALYZING = "analyzing"
    COMPLETING = "completing"
    TIMEOUT = "timeout"
    ERROR = "error"

@dataclass
class ReasoningConfig:
    """Configuration for reasoning engine."""
    max_iterations: int = 15  # Increased for more thorough investigation
    max_reasoning_time: int = 400  # 6.5 minutes - more time for thorough work
    llm_timeout: int = 45  # 45 seconds per LLM call - more time for complex reasoning
    tool_timeout: int = 90  # 90 seconds per tool call - more time for complex operations
    max_consecutive_failures: int = 3
    backoff_factor: float = 1.5
    min_backoff: float = 1.0
    max_backoff: float = 30.0

@dataclass
class ReasoningMetrics:
    """Metrics for reasoning session."""
    start_time: float
    iterations: int = 0
    tool_calls: int = 0
    llm_calls: int = 0
    failures: int = 0
    timeouts: int = 0
    
    @property
    def duration(self) -> float:
        return time.time() - self.start_time

class CircuitBreaker:
    """Circuit breaker for LLM calls to prevent cascading failures."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
    
    def can_execute(self) -> bool:
        if self.state == "closed":
            return True
        elif self.state == "open":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "half-open"
                return True
            return False
        else:  # half-open
            return True
    
    def record_success(self):
        self.failure_count = 0
        self.state = "closed"
    
    def record_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.state = "open"

class ProductionReasoningEngine:
    """Production-ready reasoning engine with robust error handling."""

    def __init__(self, agent, config: Optional[ReasoningConfig] = None):
        self.agent = agent
        self.config = config or ReasoningConfig()
        self.circuit_breaker = CircuitBreaker()
        self.metrics = None
        self.current_state = ReasoningState.STARTING

    def _validate_response_content(self, content: str) -> bool:
        """Validate that response content is meaningful and not empty."""
        if not content:
            return False

        # Strip whitespace and check if anything remains
        stripped = content.strip()
        if not stripped:
            return False

        # Check for minimal meaningful content (at least 1 non-whitespace character)
        return len(stripped) > 0

    def _log_empty_response(self, response: dict, context: str = ""):
        """Enhanced logging for empty response debugging."""
        content = response.get("content", "")
        tool_calls = response.get("tool_calls", [])

        print(f"🔍 DEBUG: Empty response detected in {context}")
        print(f"   📝 Content: '{content}' (length: {len(content)}, stripped: {len(content.strip())})")
        print(f"   🔧 Tool calls: {len(tool_calls)}")
        print(f"   📊 Response keys: {list(response.keys())}")
        print(f"   ⏱️ Current metrics: iterations={self.metrics.iterations}, tool_calls={self.metrics.tool_calls}")
        print(f"   🎯 Current state: {self.current_state.value}")

        # Log content in detail if it exists but is problematic
        if content:
            whitespace_chars = ' \t\n\r'
            print(f"   🔍 Content analysis:")
            print(f"      - Raw bytes: {repr(content[:100])}")
            print(f"      - Is whitespace only: {content.isspace()}")
            print(f"      - Contains only spaces/tabs/newlines: {all(c in whitespace_chars for c in content)}")

        logger.warning(f"Empty LLM response in {context}: content_len={len(content)}, tool_calls={len(tool_calls)}")
    
    async def reason(
        self, 
        message: str, 
        conversation_history: List[Dict] = None,
        stream: bool = False
    ) -> AsyncGenerator[str, None]:
        """Main reasoning loop with production-grade error handling."""
        
        self.metrics = ReasoningMetrics(start_time=time.time())
        self.current_state = ReasoningState.STARTING
        
        try:
            async for chunk in self._reasoning_loop(message, conversation_history, stream):
                yield chunk
        except asyncio.TimeoutError:
            self.current_state = ReasoningState.TIMEOUT
            self.metrics.timeouts += 1
            yield self._format_timeout_response(stream)
        except Exception as e:
            self.current_state = ReasoningState.ERROR
            self.metrics.failures += 1
            logger.error(f"Reasoning engine error: {e}", exc_info=True)
            yield self._format_error_response(str(e), stream)
        finally:
            self._log_metrics()
    
    async def _reasoning_loop(
        self, 
        message: str, 
        conversation_history: List[Dict],
        stream: bool
    ) -> AsyncGenerator[str, None]:
        """Core reasoning loop with timeout and circuit breaker protection."""
        
        from .prompt_builder import build_system_prompt
        from ..utils.vercel_protocol import VercelAIProtocol
        
        # Build initial messages
        system_prompt = build_system_prompt(
            self.agent.project_id, 
            self.agent.model_name, 
            conversation_history or [], 
            self.config.max_iterations
        )
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add conversation history
        if conversation_history:
            for msg in conversation_history[-4:]:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                if role in ["user", "agent", "assistant"] and content.strip():
                    llm_role = "assistant" if role == "agent" else role
                    messages.append({"role": llm_role, "content": content})
        
        # Add user message with agentic instructions
        enhanced_message = f"""{message}

IMPORTANT: You are an intelligent AI agent. Start by using your tools to research and gather comprehensive information about this topic. Be thorough, curious, and provide a detailed, well-researched response. Use multiple tools if needed to give the best possible answer."""
        messages.append({"role": "user", "content": enhanced_message})
        
        # Initialize state
        tool_conversation_history = []
        consecutive_no_tool_responses = 0
        consecutive_empty_responses = 0  # Track empty responses for retry limit
        backoff_delay = self.config.min_backoff
        
        if stream:
            yield VercelAIProtocol.reasoning("🚀 Starting intelligent reasoning process...")

        # Main reasoning loop with timeout protection
        while (self.metrics.iterations < self.config.max_iterations and
               self.metrics.duration < self.config.max_reasoning_time):
            
            self.metrics.iterations += 1
            self.current_state = ReasoningState.THINKING

            # Show engaging reasoning progress
            iteration_emojis = ["🧠", "💭", "🔍", "⚡", "🎯", "🚀", "💡", "🔬", "🎪", "🌟"]
            emoji = iteration_emojis[(self.metrics.iterations - 1) % len(iteration_emojis)]

            if stream:
                yield VercelAIProtocol.reasoning(f"{emoji} Analyzing your request...")
            
            try:
                # Check circuit breaker
                if not self.circuit_breaker.can_execute():
                    raise Exception("Circuit breaker is open - too many LLM failures")
                
                # Get LLM response with timeout
                response = await self._safe_llm_call(messages)
                self.circuit_breaker.record_success()

                # Validate response content before proceeding
                response_content = response.get("content", "")
                has_tool_calls = bool(response.get("tool_calls"))
                is_valid_content = self._validate_response_content(response_content)

                # Check for completely empty response (no valid content AND no tool calls)
                if not has_tool_calls and not is_valid_content:
                    consecutive_empty_responses += 1
                    self._log_empty_response(response, f"iteration {self.metrics.iterations}, empty #{consecutive_empty_responses}")

                    # Limit retries to prevent infinite loops
                    if consecutive_empty_responses >= 3:
                        print("⚠️ Too many empty responses, forcing minimal response...")
                        final_content = "I understand your question. Let me provide a response."
                        if stream:
                            yield VercelAIProtocol.text(final_content)
                        else:
                            yield final_content
                        return

                    print(f"🔄 Empty LLM response #{consecutive_empty_responses}/3, retrying without consuming iteration...")

                    # Add increasingly specific prompts
                    retry_prompts = [
                        "Please provide a meaningful response to continue our conversation. Even a brief answer is fine.",
                        "I need you to respond with something. Please provide any answer to the user's question.",
                        "You must provide a response. Even just 'I understand' would be acceptable."
                    ]

                    prompt_index = min(consecutive_empty_responses - 1, len(retry_prompts) - 1)
                    messages.append({
                        "role": "user",
                        "content": retry_prompts[prompt_index]
                    })
                    # Don't increment iteration count for empty responses
                    continue

                # Reset empty response counter on valid response
                consecutive_empty_responses = 0

                # Handle response
                if not has_tool_calls:
                    consecutive_no_tool_responses += 1
                    final_content = response_content or "No response generated"

                    # Debug print for short responses but accept them
                    if len(final_content.strip()) < 10:
                        print(f"📝 Short response detected ({len(final_content.strip())} chars): '{final_content.strip()}'")
                        print("💡 Accepting short response as valid")

                    # Lenient stopping criteria - accept any non-empty response
                    should_stop = (
                        consecutive_no_tool_responses >= 2 and  # Give 2 chances
                        len(final_content.strip()) > 0  # Any non-empty response is acceptable
                    )

                    if should_stop:
                        # Ensure we have a good response before stopping
                        if stream:
                            yield VercelAIProtocol.text(final_content)
                        else:
                            yield final_content
                        return

                    # Encourage more tool usage if we haven't been thorough enough
                    if self.metrics.tool_calls == 0:
                        messages.append({"role": "assistant", "content": final_content})
                        messages.append({
                            "role": "user",
                            "content": "You haven't used any tools yet! Please start by researching this topic thoroughly. Use web search, project search, or code execution to gather comprehensive information before providing your final answer."
                        })
                        consecutive_no_tool_responses = 0  # Reset to encourage tool usage
                        continue
                    elif self.metrics.tool_calls < 2 and consecutive_no_tool_responses == 1:
                        messages.append({"role": "assistant", "content": final_content})
                        messages.append({
                            "role": "user",
                            "content": "Good start, but please continue investigating! Use additional tools to gather more information and provide an even more comprehensive answer."
                        })
                        consecutive_no_tool_responses = 0  # Reset to encourage more tool usage
                        continue
                    else:
                        # Only stop if we've been thorough
                        if stream:
                            yield VercelAIProtocol.text(final_content)
                        else:
                            yield final_content
                        return
                
                # Handle tool calls
                consecutive_no_tool_responses = 0
                self.current_state = ReasoningState.TOOL_CALLING
                
                assistant_message = response.get("content", "")
                tool_calls = response.get("tool_calls", [])

                # Show reasoning content if available, otherwise show what we're doing
                if stream:
                    if self._validate_response_content(assistant_message):
                        yield VercelAIProtocol.reasoning(f"💭 {assistant_message}")
                    else:
                        # Show what we're doing even if no explicit reasoning
                        if tool_calls:
                            tool_names = [call.get('function', {}).get('name', 'tool') for call in tool_calls]
                            yield VercelAIProtocol.reasoning(f"🔧 Using tools: {', '.join(tool_names)}")
                        else:
                            yield VercelAIProtocol.reasoning(f"🤔 Processing your request...")
                
                # Add assistant message
                messages.append({
                    "role": "assistant",
                    "content": assistant_message or "I'll use tools to help answer your question.",
                    "tool_calls": tool_calls,
                })
                
                # Execute tool calls
                for tool_call in tool_calls:
                    if self.metrics.tool_calls >= self.config.max_iterations:
                        break
                    
                    self.metrics.tool_calls += 1
                    
                    if stream:
                        yield VercelAIProtocol.tool_start(tool_call.id, tool_call.function.name)
                        yield VercelAIProtocol.tool_delta(tool_call.id, tool_call.function.arguments)
                        yield VercelAIProtocol.tool_call(
                            tool_call.id,
                            tool_call.function.name,
                            json.loads(tool_call.function.arguments)
                        )
                    
                    # Execute tool with timeout
                    result = await self._safe_tool_call(tool_call)
                    
                    if stream:
                        yield VercelAIProtocol.tool_result(tool_call.id, result)
                    
                    # Add tool result to conversation
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": result or "Tool executed successfully",
                    })
                    
                    tool_conversation_history.append({
                        "tool": tool_call.function.name,
                        "args": tool_call.function.arguments,
                        "result": result,
                    })
                
                # Agentic follow-up logic - encourage continued exploration
                if tool_calls:
                    if self.metrics.iterations <= 5:  # Encourage more exploration
                        follow_up = f"Excellent! You're gathering great information. Now analyze these results and continue investigating to build an even more comprehensive answer. You have {self.config.max_iterations - self.metrics.iterations} more iterations - use them to be thorough and impressive!"
                        messages.append({"role": "user", "content": follow_up})
                        if stream:
                            yield VercelAIProtocol.reasoning("🔍 Continuing investigation...")
                    elif self.metrics.iterations <= 8:
                        follow_up = f"Great progress! You're building a solid understanding. Consider if you need any additional information or different perspectives. You still have {self.config.max_iterations - self.metrics.iterations} iterations available."
                        messages.append({"role": "user", "content": follow_up})
                        if stream:
                            yield VercelAIProtocol.reasoning("📊 Deepening analysis...")
                
                # Reset backoff on success
                backoff_delay = self.config.min_backoff
                
            except asyncio.TimeoutError:
                self.metrics.timeouts += 1
                self.circuit_breaker.record_failure()
                
                if self.metrics.timeouts >= self.config.max_consecutive_failures:
                    raise
                
                # Exponential backoff
                await asyncio.sleep(backoff_delay)
                backoff_delay = min(backoff_delay * self.config.backoff_factor, self.config.max_backoff)
                
                if stream:
                    yield VercelAIProtocol.reasoning(f"Timeout occurred, retrying with backoff...")
                
            except Exception as e:
                self.metrics.failures += 1
                self.circuit_breaker.record_failure()
                
                if self.metrics.failures >= self.config.max_consecutive_failures:
                    raise
                
                logger.warning(f"Reasoning iteration failed: {e}")
                if stream:
                    yield VercelAIProtocol.reasoning(f"Error occurred, retrying...")
                
                await asyncio.sleep(backoff_delay)
                backoff_delay = min(backoff_delay * self.config.backoff_factor, self.config.max_backoff)
        
        # If we exit the loop, provide a final response
        self.current_state = ReasoningState.COMPLETING

        if stream:
            yield VercelAIProtocol.reasoning("🎯 Finalizing comprehensive answer...")

        # Generate final answer (lenient requirements)
        final_prompt = """Now provide your FINAL answer based on your research and analysis.

Give me a helpful response that addresses the user's question. Any length is fine - even a brief answer is acceptable if it's useful."""

        final_response = await self._safe_llm_call(
            messages + [{
                "role": "user",
                "content": final_prompt
            }]
        )

        final_content = final_response.get("content", "")

        # Ensure we always have SOME response, even if minimal
        if not final_content or len(final_content.strip()) == 0:
            # Generate a minimal but valid response
            if self.metrics.tool_calls > 0:
                final_content = f"I've completed my analysis using {self.metrics.tool_calls} tools. Here's what I found based on my research."
            else:
                final_content = "I understand your question and here's my response."

            print(f"🔧 Generated fallback response: '{final_content}'")

        if stream:
            yield VercelAIProtocol.text(final_content)
        else:
            yield final_content
    
    async def _safe_llm_call(self, messages: List[Dict]) -> Dict:
        """Make LLM call with timeout protection."""
        self.metrics.llm_calls += 1
        
        return await asyncio.wait_for(
            self._execute_llm_call(messages),
            timeout=self.config.llm_timeout
        )
    
    async def _execute_llm_call(self, messages: List[Dict]) -> Dict:
        """Execute the actual LLM call."""
        # This should be made async in the future
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.agent.llm_provider.chat(
                messages=messages,
                tools=self.agent.tools,
                tool_choice="auto",
                max_tokens=1500,
                temperature=0.7,
            )
        )
    
    async def _safe_tool_call(self, tool_call) -> str:
        """Execute tool call with timeout protection."""
        return await asyncio.wait_for(
            self._execute_tool_call(tool_call),
            timeout=self.config.tool_timeout
        )
    
    async def _execute_tool_call(self, tool_call) -> str:
        """Execute the actual tool call."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.agent._execute_tool_call(tool_call)
        )
    
    def _is_complete_answer(self, content: str) -> bool:
        """Lenient heuristic - any non-empty response is considered complete."""
        return len(content.strip()) > 0  # Accept any non-empty response
    
    def _format_timeout_response(self, stream: bool) -> str:
        """Format timeout response."""
        from ..utils.vercel_protocol import VercelAIProtocol
        
        message = f"I've reached the maximum reasoning time ({self.config.max_reasoning_time}s). Let me provide an answer based on what I've found so far."
        
        if stream:
            return VercelAIProtocol.text(message)
        return message
    
    def _format_error_response(self, error: str, stream: bool) -> str:
        """Format error response."""
        from ..utils.vercel_protocol import VercelAIProtocol
        
        message = f"I encountered an error during reasoning: {error}. Please try again."
        
        if stream:
            return VercelAIProtocol.text(message)
        return message
    
    def _log_metrics(self):
        """Log reasoning session metrics."""
        logger.info(f"Reasoning session completed: "
                   f"duration={self.metrics.duration:.2f}s, "
                   f"iterations={self.metrics.iterations}, "
                   f"tool_calls={self.metrics.tool_calls}, "
                   f"llm_calls={self.metrics.llm_calls}, "
                   f"failures={self.metrics.failures}, "
                   f"timeouts={self.metrics.timeouts}, "
                   f"final_state={self.current_state.value}")
