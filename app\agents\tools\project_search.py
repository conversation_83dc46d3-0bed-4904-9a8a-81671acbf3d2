from typing import Any
import json

def run(agent: Any, query: str, k: int = 5) -> str:
    if not agent.document_manager:
        return json.dumps({"error": "No project loaded"})
    try:
        results = agent.document_manager.vector_search(query, k)
        return json.dumps({
            "found": len(results),
            "query": query,
            "results": [{
                "content": r["content"],
                "score": r["score"],
                "metadata": r.get("metadata", {}),
            } for r in results],
        })
    except Exception as e:
        return json.dumps({"error": str(e)})

