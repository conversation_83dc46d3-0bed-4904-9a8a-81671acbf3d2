# 🚀 FastMCP Integration

Modern MCP (Model Context Protocol) integration using the [FastMCP](https://gofastmcp.com/) library. This provides a clean, configuration-based approach to managing MCP servers and tools.

## ✨ Features

- **Configuration-based**: JSON configuration for all MCP servers
- **Multi-transport support**: STDIO, HTTP, SSE transports
- **Hot reloading**: Add/remove servers without restart
- **Tool caching**: Efficient tool discovery and caching
- **REST API**: Full management via HTTP endpoints
- **Agent integration**: Seamless integration with BaseAgent

## 🛠️ Installation

```bash
pip install fastmcp
```

## 📋 Configuration

MCP servers are configured in `config/mcp_config.json`:

```json
{
  "mcpServers": {
    "example_server": {
      "transport": "stdio",
      "command": "python",
      "args": ["example_mcp_server.py"],
      "env": {
        "DEBUG": "true"
      },
      "description": "Example MCP server for testing"
    },
    "weather_api": {
      "transport": "http",
      "url": "https://weather-api.example.com/mcp",
      "headers": {
        "Authorization": "Bearer your-token-here"
      },
      "description": "Weather API MCP server"
    }
  }
}
```

### Transport Types

#### STDIO Transport
For local MCP servers:
```json
{
  "transport": "stdio",
  "command": "python",
  "args": ["server.py", "--verbose"],
  "env": {
    "API_KEY": "secret",
    "LOG_LEVEL": "DEBUG"
  },
  "cwd": "/path/to/server"
}
```

#### HTTP Transport
For remote MCP servers:
```json
{
  "transport": "http",
  "url": "https://api.example.com/mcp",
  "headers": {
    "Authorization": "Bearer token",
    "X-Custom-Header": "value"
  }
}
```

#### SSE Transport (Legacy)
For Server-Sent Events:
```json
{
  "transport": "sse",
  "url": "https://api.example.com/sse",
  "headers": {
    "Authorization": "Bearer token"
  }
}
```

## 🔧 Usage

### Programmatic API

```python
from app.utils.fastmcp_client import get_mcp_manager

# Get manager instance
mcp_manager = get_mcp_manager()

# Add a server
server_config = {
    "transport": "stdio",
    "command": "python",
    "args": ["my_server.py"]
}
mcp_manager.add_server("my_server", server_config)

# List servers
servers = mcp_manager.list_servers()

# Get tools
tools = await mcp_manager.get_tools()

# Call a tool
result = await mcp_manager.call_tool("tool_name", {"arg": "value"})
```

### REST API Endpoints

#### List Servers
```http
GET /mcp/servers
```

#### Add Server
```http
POST /mcp/servers
Content-Type: application/json

{
  "name": "my_server",
  "transport": "stdio",
  "command": "python",
  "args": ["server.py"],
  "description": "My MCP server"
}
```

#### Remove Server
```http
DELETE /mcp/servers/{server_name}
```

#### List Tools
```http
GET /mcp/tools
```

#### Refresh Tools
```http
POST /mcp/tools/refresh
```

### Agent Integration

MCP tools are automatically available to the AI agent:

```python
# The agent can now use MCP tools directly
response = await agent.reply("Use the weather tool to get forecast for London")
```

The agent also has a built-in tool to add MCP servers:

```
Agent: I can add a new MCP server for you. What type of server would you like to add?
User: Add a weather server at https://weather.example.com/mcp
Agent: *calls add_mcp_server tool*
```

## 🔄 Tool Discovery

Tools are discovered automatically from all configured servers:

1. **Built-in tools**: Always available (web_search, code_execution, etc.)
2. **MCP tools**: Loaded from all configured servers
3. **Management tools**: add_mcp_server for dynamic configuration

Tools are cached for performance and refreshed when:
- Servers are added/removed
- Manual refresh is triggered
- Agent cache is cleared

## 🎯 Benefits Over Old System

### ✅ Modern FastMCP Integration
- **Standards-compliant**: Uses official FastMCP library
- **Multi-server support**: Connect to multiple MCP servers
- **Transport flexibility**: STDIO, HTTP, SSE support
- **Configuration-driven**: JSON-based server management

### ✅ Performance Improvements
- **Agent caching**: BaseAgent instances cached per project
- **Tool caching**: Tools cached until configuration changes
- **Hot reloading**: Add servers without restart
- **Efficient discovery**: Batch tool loading

### ✅ Developer Experience
- **Clean API**: Simple, intuitive interface
- **REST management**: Full HTTP API for server management
- **Error handling**: Comprehensive error reporting
- **Debugging**: Clear logging and status endpoints

### ✅ Production Ready
- **Robust error handling**: Graceful degradation
- **Configuration validation**: Proper config validation
- **Cache management**: Intelligent cache invalidation
- **Monitoring**: Status and health endpoints

## 🚀 Migration from Old System

The old `tools.json` and custom MCP client have been completely replaced:

### Old System (Removed)
- ❌ `config/tools.json`
- ❌ `app/utils/mcp_client.py`
- ❌ Custom HTTP endpoint discovery
- ❌ Manual tool flattening

### New System
- ✅ `config/mcp_config.json`
- ✅ `app/utils/fastmcp_client.py`
- ✅ FastMCP library integration
- ✅ Automatic tool discovery

## 🔍 Debugging

### Check Server Status
```http
GET /mcp/servers
```

### Check Available Tools
```http
GET /mcp/tools
```

### Check Cache Status
```http
GET /cache/status
```

### Refresh Everything
```http
POST /mcp/tools/refresh
POST /cache/clear
```

## 📝 Example Servers

### Simple STDIO Server
```json
{
  "my_tools": {
    "transport": "stdio",
    "command": "python",
    "args": ["tools_server.py"],
    "description": "Local tools server"
  }
}
```

### Remote HTTP Server
```json
{
  "api_tools": {
    "transport": "http",
    "url": "https://api.mycompany.com/mcp",
    "headers": {
      "Authorization": "Bearer ${API_TOKEN}"
    },
    "description": "Company API tools"
  }
}
```

### Development Server
```json
{
  "dev_server": {
    "transport": "stdio",
    "command": "uvx",
    "args": ["--from", "my-mcp-package", "my-mcp-server"],
    "env": {
      "DEBUG": "1",
      "LOG_LEVEL": "INFO"
    },
    "description": "Development MCP server"
  }
}
```

## 🎉 Ready to Use!

The FastMCP integration is now live and ready to use. Your AI agent can:

1. **Use existing MCP tools** from configured servers
2. **Add new MCP servers** dynamically via conversation
3. **Discover tools automatically** from all servers
4. **Handle multiple transports** (STDIO, HTTP, SSE)
5. **Cache efficiently** for fast performance

Start by adding your first MCP server to `config/mcp_config.json` or use the REST API!
