"""Plug-and-play embedding providers."""

import os
from typing import List, Dict, Any
from abc import ABC, abstractmethod

class EmbeddingProvider(ABC):
    """Base embedding provider interface."""
    
    @abstractmethod
    def embed_text(self, text: str) -> List[float]:
        pass
    
    @abstractmethod
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        pass

class OpenAIEmbeddings(EmbeddingProvider):
    """OpenAI embedding provider."""
    
    def __init__(self, model: str = "text-embedding-3-small"):
        from openai import OpenAI
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.model = model
    
    def embed_text(self, text: str) -> List[float]:
        response = self.client.embeddings.create(input=text, model=self.model)
        return response.data[0].embedding
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        response = self.client.embeddings.create(input=texts, model=self.model)
        return [data.embedding for data in response.data]

    def embed_query(self, text: str) -> List[float]:
        """LangChain compatibility method for query embedding."""
        return self.embed_text(text)

class GoogleEmbeddings(EmbeddingProvider):
    """Google embedding provider."""
    
    def __init__(self, model: str = "models/text-embedding-004"):
        import google.generativeai as genai
        genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
        self.model = model
        self.client = genai
    
    def embed_text(self, text: str) -> List[float]:
        result = self.client.embed_content(
            model=self.model,
            content=text,
            task_type="retrieval_document"
        )
        return result['embedding']
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        embeddings = []
        for text in texts:
            result = self.client.embed_content(
                model=self.model,
                content=text,
                task_type="retrieval_document"
            )
            embeddings.append(result['embedding'])
        return embeddings

    def embed_query(self, text: str) -> List[float]:
        """LangChain compatibility method for query embedding."""
        result = self.client.embed_content(
            model=self.model,
            content=text,
            task_type="retrieval_query"
        )
        return result['embedding']

class OfflineEmbeddings(EmbeddingProvider):
    """Offline embedding provider using sentence-transformers."""
    
    def __init__(self, model: str = "all-MiniLM-L6-v2"):
        from sentence_transformers import SentenceTransformer
        self.model = SentenceTransformer(model)
    
    def embed_text(self, text: str) -> List[float]:
        return self.model.encode(text).tolist()
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        return self.model.encode(texts).tolist()

    def embed_query(self, text: str) -> List[float]:
        """LangChain compatibility method for query embedding."""
        return self.embed_text(text)

def get_embedding_provider(provider: str = None, **kwargs) -> EmbeddingProvider:
    """Factory function to get embedding provider."""
    provider = provider or os.getenv("EMBEDDING_PROVIDER", "openai")
    
    providers = {
        "openai": OpenAIEmbeddings,
        "google": GoogleEmbeddings,
        "offline": OfflineEmbeddings
    }
    
    if provider not in providers:
        raise ValueError(f"Unknown provider: {provider}. Available: {list(providers.keys())}")
    
    try:
        return providers[provider](**kwargs)
    except Exception as e:
        print(f"Failed to initialize {provider} embeddings: {e}")
        # Fallback to offline
        if provider != "offline":
            print("Falling back to offline embeddings...")
            return OfflineEmbeddings(**kwargs)
        raise
