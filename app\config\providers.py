"""Provider configuration utilities for easy switching between LLM and embedding providers."""

import os
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ProviderConfig:
    """Configuration manager for LLM and embedding providers."""
    
    # Available providers and their default models
    LLM_PROVIDERS = {
        "openai": {
            "default_model": "gpt-4o-mini",
            "models": ["gpt-4o-mini", "gpt-4o", "gpt-3.5-turbo"],
            "env_key": "OPENAI_API_KEY",
            "description": "OpenAI GPT models with excellent tool support"
        },
        "gemini": {
            "default_model": "gemini-1.5-flash",
            "models": ["gemini-1.5-flash", "gemini-1.5-pro", "gemini-pro"],
            "env_key": "GOOGLE_API_KEY",
            "description": "Google Gemini models with function calling support"
        }
    }
    
    EMBEDDING_PROVIDERS = {
        "openai": {
            "default_model": "text-embedding-3-small",
            "models": ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"],
            "env_key": "OPENAI_API_KEY",
            "description": "OpenAI embedding models"
        },
        "google": {
            "default_model": "models/text-embedding-004",
            "models": ["models/text-embedding-004", "models/embedding-001"],
            "env_key": "GOOGLE_API_KEY",
            "description": "Google embedding models"
        },
        "offline": {
            "default_model": "sentence-transformers/all-MiniLM-L6-v2",
            "models": ["sentence-transformers/all-MiniLM-L6-v2", "sentence-transformers/all-mpnet-base-v2"],
            "env_key": None,
            "description": "Offline embedding models (no API key required)"
        }
    }
    
    @classmethod
    def get_current_config(cls) -> Dict:
        """Get current provider configuration from environment."""
        return {
            "llm_provider": os.getenv("LLM_PROVIDER", "gemini"),
            "embedding_provider": os.getenv("EMBEDDING_PROVIDER", "google"),
            "llm_model": os.getenv("LLM_MODEL"),
            "embedding_model": os.getenv("EMBEDDING_MODEL")
        }
    
    @classmethod
    def validate_config(cls) -> Dict:
        """Validate current configuration and return status."""
        config = cls.get_current_config()
        status = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Check LLM provider
        llm_provider = config["llm_provider"]
        if llm_provider not in cls.LLM_PROVIDERS:
            status["valid"] = False
            status["errors"].append(f"Unknown LLM provider: {llm_provider}")
        else:
            env_key = cls.LLM_PROVIDERS[llm_provider]["env_key"]
            if env_key and not os.getenv(env_key):
                status["valid"] = False
                status["errors"].append(f"Missing API key: {env_key}")
        
        # Check embedding provider
        embedding_provider = config["embedding_provider"]
        if embedding_provider not in cls.EMBEDDING_PROVIDERS:
            status["valid"] = False
            status["errors"].append(f"Unknown embedding provider: {embedding_provider}")
        else:
            env_key = cls.EMBEDDING_PROVIDERS[embedding_provider]["env_key"]
            if env_key and not os.getenv(env_key):
                status["valid"] = False
                status["errors"].append(f"Missing API key: {env_key}")
        
        return status
    
    @classmethod
    def get_recommended_config(cls) -> Dict:
        """Get recommended configuration based on available API keys."""
        config = {"llm_provider": "gemini", "embedding_provider": "google"}
        
        # Check what API keys are available
        has_openai = bool(os.getenv("OPENAI_API_KEY"))
        has_google = bool(os.getenv("GOOGLE_API_KEY"))
        
        if has_google:
            config["llm_provider"] = "gemini"
            config["embedding_provider"] = "google"
        elif has_openai:
            config["llm_provider"] = "openai"
            config["embedding_provider"] = "openai"
        else:
            config["llm_provider"] = "gemini"  # Will fail but show clear error
            config["embedding_provider"] = "offline"  # Fallback to offline embeddings
        
        return config
    
    @classmethod
    def print_status(cls):
        """Print current configuration status."""
        config = cls.get_current_config()
        status = cls.validate_config()
        
        print("🔧 Provider Configuration Status")
        print("=" * 40)
        print(f"LLM Provider: {config['llm_provider']}")
        print(f"Embedding Provider: {config['embedding_provider']}")
        
        if config['llm_model']:
            print(f"LLM Model: {config['llm_model']}")
        if config['embedding_model']:
            print(f"Embedding Model: {config['embedding_model']}")
        
        print("\n📊 Status:")
        if status["valid"]:
            print("✅ Configuration is valid")
        else:
            print("❌ Configuration has errors:")
            for error in status["errors"]:
                print(f"  - {error}")
        
        if status["warnings"]:
            print("\n⚠️  Warnings:")
            for warning in status["warnings"]:
                print(f"  - {warning}")
        
        print("\n🔑 Available Providers:")
        for provider, info in cls.LLM_PROVIDERS.items():
            env_key = info["env_key"]
            has_key = "✅" if not env_key or os.getenv(env_key) else "❌"
            print(f"  {has_key} {provider}: {info['description']}")

def main():
    """CLI utility to check and configure providers."""
    ProviderConfig.print_status()
    
    print("\n💡 Recommendations:")
    recommended = ProviderConfig.get_recommended_config()
    print(f"Set LLM_PROVIDER={recommended['llm_provider']}")
    print(f"Set EMBEDDING_PROVIDER={recommended['embedding_provider']}")

if __name__ == "__main__":
    main()
