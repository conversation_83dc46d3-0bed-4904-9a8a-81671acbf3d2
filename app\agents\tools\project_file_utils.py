"""
Project-aware file utilities for seamless file operations between find_files and code_execution.
Provides consistent file path handling and easy access patterns.
"""

import os
import glob
from pathlib import Path
from typing import List, Dict, Optional, Any

class ProjectFileManager:
    """Manages file operations within a project structure."""
    
    def __init__(self, project_id: str = None):
        self.project_id = project_id
        self.project_root = f"projects/{project_id}" if project_id else "."
        self.work_dir = f"{self.project_root}/work"
        self.input_dir = f"{self.project_root}/input"
        self.output_dir = f"{self.project_root}/output"
        
        # Ensure directories exist
        for directory in [self.project_root, self.work_dir, self.input_dir, self.output_dir]:
            os.makedirs(directory, exist_ok=True)
    
    def find_files(self, pattern: str = "*") -> List[Dict[str, Any]]:
        """Find files and return structured information with correct paths."""
        results = []
        
        # Search with glob patterns
        if "*" in pattern or "?" in pattern:
            found_files = glob.glob(f"{self.project_root}/**/{pattern}", recursive=True)
        else:
            # Search by filename substring and extension
            found_files = []
            for root in Path(self.project_root).rglob("*"):
                if root.is_file() and pattern.lower() in root.name.lower():
                    found_files.append(str(root))
            
            # Also search by extension
            if not pattern.startswith("*") and "." not in pattern:
                found_files.extend(glob.glob(f"{self.project_root}/**/*.{pattern}", recursive=True))
        
        # Process found files
        work_path = Path(self.work_dir).resolve()
        project_path = Path(self.project_root).resolve()
        
        for file_path in sorted(set(found_files)):
            abs_path = Path(file_path).resolve()
            if not abs_path.exists():
                continue
                
            # Calculate relative paths
            try:
                rel_from_work = os.path.relpath(abs_path, work_path)
            except ValueError:
                rel_from_work = str(abs_path)
            
            try:
                rel_from_project = os.path.relpath(abs_path, project_path)
            except ValueError:
                rel_from_project = str(abs_path)
            
            # Determine directory type
            if "/input/" in str(abs_path):
                dir_type = "input"
            elif "/output/" in str(abs_path):
                dir_type = "output"
            elif "/work/" in str(abs_path):
                dir_type = "work"
            else:
                dir_type = "other"
            
            file_info = {
                "name": abs_path.name,
                "absolute_path": str(abs_path),
                "relative_from_project": rel_from_project,
                "relative_from_work": rel_from_work,
                "directory_type": dir_type,
                "size": abs_path.stat().st_size,
                "extension": abs_path.suffix,
            }
            results.append(file_info)
        
        return results
    
    def get_file_access_code(self, filename: str) -> str:
        """Generate code snippets for accessing a file."""
        files = self.find_files(filename)
        if not files:
            return f"# File '{filename}' not found in project"
        
        file_info = files[0]  # Use first match
        code_snippets = []
        
        # CSV loading
        if file_info["extension"].lower() == ".csv":
            code_snippets.append(f"""
# Load CSV file
import pandas as pd
df = pd.read_csv('{file_info["relative_from_work"]}')
print(f"Loaded {{df.shape[0]}} rows and {{df.shape[1]}} columns")
print(df.head())""")
        
        # Text file loading
        elif file_info["extension"].lower() in [".txt", ".md", ".json"]:
            code_snippets.append(f"""
# Load text file
with open('{file_info["relative_from_work"]}', 'r') as f:
    content = f.read()
print(f"File size: {{len(content)}} characters")
print(content[:200] + "..." if len(content) > 200 else content)""")
        
        # Excel loading
        elif file_info["extension"].lower() in [".xlsx", ".xls"]:
            code_snippets.append(f"""
# Load Excel file
import pandas as pd
df = pd.read_excel('{file_info["relative_from_work"]}')
print(f"Loaded {{df.shape[0]}} rows and {{df.shape[1]}} columns")
print(df.head())""")
        
        # Generic file access
        code_snippets.append(f"""
# Generic file access
import os
filepath = '{file_info["relative_from_work"]}'
print(f"File exists: {{os.path.exists(filepath)}}")
print(f"File size: {{os.path.getsize(filepath) if os.path.exists(filepath) else 0}} bytes")""")
        
        return "\n".join(code_snippets)
    
    def format_find_results(self, pattern: str) -> str:
        """Format file search results in a clean, actionable way."""
        files = self.find_files(pattern)

        if not files:
            return f"❌ No files found matching '{pattern}'"

        # Group by directory type
        by_type = {}
        for file_info in files:
            dir_type = file_info["directory_type"]
            if dir_type not in by_type:
                by_type[dir_type] = []
            by_type[dir_type].append(file_info)

        output = [f"📁 Found {len(files)} files matching '{pattern}':\n"]

        # Display files by type with clean formatting
        type_icons = {
            "input": "📥",
            "output": "📤",
            "work": "🔧",
            "other": "📄"
        }

        for dir_type, type_files in by_type.items():
            icon = type_icons.get(dir_type, "📄")

            for file_info in type_files[:5]:  # Show top 5 files per type
                output.append(f"{icon} {file_info['name']} ({file_info['size']} bytes)")
                output.append(f"   💻 {file_info['relative_from_work']}")

            if len(type_files) > 5:
                output.append(f"   ... and {len(type_files) - 5} more {dir_type} files")

        # Add concise usage example for the first file
        if files:
            example = files[0]
            output.append(f"\n💡 Quick usage:")

            if example["extension"].lower() == ".csv":
                output.append(f"   df = load_csv('{example['name']}')")
            else:
                output.append(f"   content = load_file('{example['name']}')")

        return "\n".join(output)

def get_project_file_manager(agent: Any) -> ProjectFileManager:
    """Get a ProjectFileManager instance for the agent."""
    project_id = getattr(agent, 'project_id', None)
    return ProjectFileManager(project_id)
