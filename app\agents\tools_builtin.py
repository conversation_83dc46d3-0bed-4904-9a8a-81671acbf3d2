from typing import List, Dict

def get_builtin_tools() -> List[Dict]:
    """Return the built-in tool specs used by BaseAgent.
    Kept minimal and identical to previous inline definitions.
    """
    return [
        {
            "type": "function",
            "function": {
                "name": "find_files",
                "description": "Find files in the project directory with fuzzy search. Use this FIRST to understand file structure.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pattern": {
                            "type": "string",
                            "description": "File pattern to search for (e.g., '*.csv', 'data', 'titanic')",
                        }
                    },
                    "required": ["pattern"],
                },
            },
        },
        {
            "type": "function",
            "function": {
                "name": "project_search",
                "description": "Search through project documents and knowledge base for relevant information. Use multiple searches to build comprehensive understanding.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query - be specific and try different angles",
                        },
                        "k": {
                            "type": "integer",
                            "description": "Number of results (default: 5, max: 20)",
                        },
                    },
                    "required": ["query"],
                },
            },
        },
        {
            "type": "function",
            "function": {
                "name": "web_search",
                "description": "Search the web for current information and scrape content from URLs. Always summarize findings and cite sources in your final answer.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query or URL to scrape - be specific for better results",
                        },
                        "is_url": {
                            "type": "boolean",
                            "description": "True if query is a URL to scrape, False for web search",
                        },
                    },
                    "required": ["query"],
                },
            },
        },
        {
            "type": "function",
            "function": {
                "name": "code_execution",
                "description": "Execute Python code for data analysis, directory exploration, file processing, and learning project structure. Includes pandas, numpy, and helper functions for directory analysis.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "code": {
                            "type": "string",
                            "description": "Python code to execute. Use analyze_directory() and get_file_info() for project exploration.",
                        },
                        "filename": {
                            "type": "string",
                            "description": "Optional filename to save code for reuse",
                        },
                    },
                    "required": ["code"],
                },
            },
        },
    ]

