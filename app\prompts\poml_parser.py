"""
POML (Prompt Oriented Markup Language) Parser
Using Microsoft's official POML library for prompt modularity and management.

Supported POML tags:
- <role>: Define the AI's role and persona
- <task>: Specify the main task or objective
- <instruction>: Provide specific behavioral instructions
- <example>: Include examples for better understanding
- <output-format>: Define expected output format
- <document>: Reference external documents
- <table>: Include tabular data
- <img>: Reference images
- Variables: {{ variable_name }} for dynamic content
"""

import os
import yaml
import re
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging

try:
    import poml
    POML_AVAILABLE = True
except ImportError:
    POML_AVAILABLE = False
    logging.warning("Microsoft POML library not available. Install with: pip install poml")

logger = logging.getLogger(__name__)

class POMLParser:
    """Parser for POML using Microsoft's official POML library with fallback."""
    
    def __init__(self, prompts_dir: str = "app/prompts"):
        self.prompts_dir = Path(prompts_dir)
        self.cache = {}
        self.templates = {}
        self.components = {}
        
        if not POML_AVAILABLE:
            logger.warning("POML library not available, using fallback mode")
            self.fallback_mode = True
        else:
            self.fallback_mode = False
            
        self._load_all_prompts()
    
    def _load_all_prompts(self):
        """Load all POML files from the prompts directory with proper segregation."""
        if not self.prompts_dir.exists():
            self.prompts_dir.mkdir(parents=True, exist_ok=True)
            return

        # Load components first (shared between system and user)
        components_dir = self.prompts_dir / "components"
        if components_dir.exists():
            for file_path in components_dir.glob("*.poml"):
                self._load_component(file_path)

        # Load system prompts
        system_dir = self.prompts_dir / "system"
        if system_dir.exists():
            for file_path in system_dir.glob("*.poml"):
                self._load_template(file_path, prefix="system/")

        # Load user prompts
        user_dir = self.prompts_dir / "user"
        if user_dir.exists():
            for file_path in user_dir.glob("*.poml"):
                self._load_template(file_path, prefix="user/")

        # Load legacy templates (for backward compatibility)
        templates_dir = self.prompts_dir / "templates"
        if templates_dir.exists():
            for file_path in templates_dir.glob("*.poml"):
                self._load_template(file_path)
    
    def _load_component(self, file_path: Path):
        """Load a POML component file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            component = self._parse_poml_content(content)
            component_name = file_path.stem
            self.components[component_name] = component
            
            logger.info(f"Loaded POML component: {component_name}")
        except Exception as e:
            logger.error(f"Error loading component {file_path}: {e}")
    
    def _load_template(self, file_path: Path, prefix: str = ""):
        """Load a POML template file with optional prefix for organization."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            template = self._parse_poml_content(content)
            template_name = prefix + file_path.stem
            self.templates[template_name] = template

            logger.info(f"Loaded POML template: {template_name}")
        except Exception as e:
            logger.error(f"Error loading template {file_path}: {e}")
    
    def _parse_poml_content(self, content: str) -> Dict[str, Any]:
        """Parse POML content using Microsoft POML library or fallback."""
        if not self.fallback_mode and POML_AVAILABLE:
            try:
                # Use Microsoft POML library
                return self._parse_with_poml_library(content)
            except Exception as e:
                logger.warning(f"POML library parsing failed, using fallback: {e}")
        
        # Fallback parsing
        return self._parse_fallback(content)
    
    def _parse_with_poml_library(self, content: str) -> Dict[str, Any]:
        """Parse using Microsoft POML library."""
        try:
            # Microsoft POML doesn't have a parse method, it processes content directly
            return {
                'metadata': {},
                'content': content,
                'poml_content': content,  # Store original POML content
                'variables': self._extract_variables_fallback(content),
                'includes': []  # POML handles includes internally
            }
        except Exception as e:
            logger.error(f"Microsoft POML parsing error: {e}")
            raise
    
    def _parse_fallback(self, content: str) -> Dict[str, Any]:
        """Fallback parsing for when POML library is not available."""
        # Split content into metadata and prompt sections
        parts = content.split('---', 2)
        
        if len(parts) >= 2:
            # Has YAML frontmatter
            try:
                metadata = yaml.safe_load(parts[1]) or {}
            except yaml.YAMLError:
                metadata = {}
            
            prompt_content = parts[2] if len(parts) > 2 else ""
        else:
            # No frontmatter, treat as plain prompt
            metadata = {}
            prompt_content = content
        
        return {
            'metadata': metadata,
            'content': prompt_content.strip(),
            'variables': self._extract_variables_fallback(prompt_content),
            'includes': self._extract_includes_fallback(prompt_content)
        }
    
    def _extract_variables_fallback(self, content: str) -> List[str]:
        """Extract variable placeholders from content."""
        variables = set()
        
        # Find {{variable}} patterns
        var_pattern = re.findall(r'\{\{(\w+)\}\}', content)
        variables.update(var_pattern)
        
        return list(variables)
    
    def _extract_includes_fallback(self, content: str) -> List[str]:
        """Extract include statements from content."""
        includes = set()
        
        # Find {% include component_name %} patterns
        legacy_includes = re.findall(r'\{\%\s*include\s+(\w+)\s*\%\}', content)
        includes.update(legacy_includes)
        
        return list(includes)
    
    def render_prompt(self, template_name: str, variables: Dict[str, Any] = None, **kwargs) -> str:
        """Render a prompt template with variables."""
        variables = variables or {}
        variables.update(kwargs)
        
        # Get template
        if template_name not in self.templates:
            raise ValueError(f"Template '{template_name}' not found")
        
        template = self.templates[template_name]
        
        if not self.fallback_mode and 'poml_content' in template:
            try:
                # Use Microsoft POML rendering
                return self._render_with_poml_library(template, variables)
            except Exception as e:
                logger.warning(f"POML library rendering failed, using fallback: {e}")
        
        # Fallback rendering
        return self._render_fallback(template, variables)
    
    def _render_with_poml_library(self, template: Dict[str, Any], variables: Dict[str, Any]) -> str:
        """Render using Microsoft POML library."""
        try:
            # Get the POML content
            poml_content = template.get('poml_content', template.get('content', ''))

            # Process variables in the content first
            processed_content = self._process_variables_fallback(poml_content, variables)

            # Use Microsoft POML to process the content
            result = poml.run('--input', processed_content)

            # POML outputs JSON, we need to extract the content
            if result.returncode == 0:
                # The output is printed to stdout during execution, not returned
                # For now, return the processed content
                return processed_content.replace('<poml>', '').replace('</poml>', '').strip()
            else:
                raise Exception(f"POML execution failed with return code {result.returncode}")

        except Exception as e:
            logger.error(f"Microsoft POML rendering error: {e}")
            raise
    
    def _render_fallback(self, template: Dict[str, Any], variables: Dict[str, Any]) -> str:
        """Fallback rendering method."""
        content = template['content']
        
        # Process includes first
        content = self._process_includes_fallback(content)
        
        # Process variables
        content = self._process_variables_fallback(content, variables)
        
        return content.strip()
    
    def _process_includes_fallback(self, content: str) -> str:
        """Process include statements in content."""
        def replace_include(match):
            component_name = match.group(1)
            if component_name in self.components:
                component_content = self.components[component_name]['content']
                # Recursively process includes in components
                return self._process_includes_fallback(component_content)
            else:
                logger.warning(f"Component '{component_name}' not found")
                return f"[MISSING COMPONENT: {component_name}]"
        
        return re.sub(r'\{\%\s*include\s+(\w+)\s*\%\}', replace_include, content)
    
    def _process_variables_fallback(self, content: str, variables: Dict[str, Any]) -> str:
        """Process variable substitutions in content."""
        def replace_variable(match):
            var_name = match.group(1)
            if var_name in variables:
                return str(variables[var_name])
            else:
                logger.warning(f"Variable '{var_name}' not provided")
                return f"[MISSING: {var_name}]"
        
        return re.sub(r'\{\{(\w+)\}\}', replace_variable, content)
    
    def get_component(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a component by name."""
        return self.components.get(name)
    
    def get_template(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a template by name."""
        return self.templates.get(name)
    
    def list_templates(self) -> List[str]:
        """List all available templates."""
        return list(self.templates.keys())
    
    def list_components(self) -> List[str]:
        """List all available components."""
        return list(self.components.keys())
    
    def get_system_prompt(self, template_name: str, **variables) -> str:
        """Render a system prompt template."""
        return self.render_prompt(f"system/{template_name}", variables)

    def get_user_prompt(self, template_name: str, **variables) -> str:
        """Render a user prompt template."""
        return self.render_prompt(f"user/{template_name}", variables)

    def reload(self):
        """Reload all prompts from disk."""
        self.cache.clear()
        self.components.clear()
        self.templates.clear()
        self._load_all_prompts()
        logger.info("Reloaded all POML prompts")

# Global POML parser instance
_poml_parser = None

def get_poml_parser() -> POMLParser:
    """Get the global POML parser instance."""
    global _poml_parser
    if _poml_parser is None:
        _poml_parser = POMLParser()
    return _poml_parser

def render_prompt(template_name: str, **variables) -> str:
    """Convenience function to render a prompt."""
    parser = get_poml_parser()
    return parser.render_prompt(template_name, variables)

def reload_prompts():
    """Reload all prompts from disk."""
    parser = get_poml_parser()
    parser.reload()
