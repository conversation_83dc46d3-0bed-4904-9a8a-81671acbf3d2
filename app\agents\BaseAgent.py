from typing import Dict
import json
import os
from ..managers.DocumentManager import DocumentManager
from ..providers.llm import get_llm_provider
from ..utils.vercel_protocol import VercelAIProtocol
from .tools_manager import get_all_tools_sync
from .tools import get_tools_registry
from ..utils.fastmcp_client import call_mcp_tool, add_mcp_server
from .reasoning_engine import ProductionReasoningEngine, ReasoningConfig



class BaseAgent:
    """Simple, powerful AI agent with Graph-RAG, web search, and code execution capabilities."""

    access_specifier: str = "General"
    model_name: str = "gemini-2.5-flash"  # Default to Gemini
    llm_provider_type: str = "gemini"  # Default provider type

    def __init__(
        self,
        project_id: str = None,
        model_name: str = None,
        access_specifier: str = None,
        db_config: Dict = None,
        llm_provider: str = None,
    ):
        self.project_id = project_id
        self.model_name = model_name or self.model_name
        self.access_specifier = access_specifier or self.access_specifier
        self.llm_provider_type = llm_provider or os.getenv(
            "LLM_PROVIDER", self.llm_provider_type
        )

        # Initialize components safely
        self.document_manager = None
        self.llm_provider = None

        # Try to initialize document manager if project_id provided
        if project_id:
            try:
                self.document_manager = DocumentManager(
                    project_id=project_id, db_config=db_config
                )
            except Exception as e:
                print(f"Warning: Could not initialize DocumentManager: {e}")

        # Try to initialize LLM provider with correct provider type
        try:
            self.llm_provider = get_llm_provider(
                provider=self.llm_provider_type, model=self.model_name
            )
            print(
                f"✅ Initialized LLM provider: {self.llm_provider_type} with model: {self.model_name}"
            )
        except Exception as e:
            print(f"Warning: Could not initialize LLM provider: {e}")

        # Create work directory
        if project_id:
            self.work_dir = f"projects/{project_id}/work"
            os.makedirs(self.work_dir, exist_ok=True)
        else:
            self.work_dir = "work"
            os.makedirs(self.work_dir, exist_ok=True)

        # Don't cache tools - they need to be refreshed on each request
        # to pick up new MCP servers from tools.json
        self._tools_cache = None

    @property
    def tools(self):
        """Get fresh tools list on each access to pick up new MCP servers."""
        # Load all tools: built-in + MCP from FastMCP configuration
        tools = get_all_tools_sync(self.project_id)

        # Add tool for users to add MCP servers
        tools += [
            {
                "type": "function",
                "function": {
                    "name": "add_mcp_server",
                    "description": "Add a new MCP server to the configuration.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string", "description": "Server name"},
                            "transport": {"type": "string", "description": "Transport type (stdio, http, sse)", "enum": ["stdio", "http", "sse"]},
                            "command": {"type": "string", "description": "Command to run (for stdio transport)"},
                            "args": {"type": "array", "items": {"type": "string"}, "description": "Command arguments (for stdio transport)"},
                            "url": {"type": "string", "description": "Server URL (for http/sse transport)"},
                            "env": {"type": "object", "description": "Environment variables (for stdio transport)"},
                            "description": {"type": "string", "description": "Server description"},
                        },
                        "required": ["name", "transport"],
                    },
                },
            },
        ]
        return tools



    async def reply(
        self,
        message: str,
        context: Dict = None,
        max_tool_calls: int = 10,  # Reduced from 25 to prevent excessive iterations
        stream: bool = False,
    ):
        """Main method to handle user messages with production-ready reasoning engine."""
        if not self.llm_provider:
            yield VercelAIProtocol.error("LLM provider not initialized. Please check your API keys.")
            yield VercelAIProtocol.finish("error")
            return

        try:
            print(f"🧠 BaseAgent.reply called with message: {message[:100]}...")
            print(f"🔧 Max tool calls: {max_tool_calls}, Streaming: {stream}")

            # Extract conversation history from context if available
            conversation_history = []
            if context and "conversation_history" in context:
                conversation_history = context["conversation_history"]
                print(f"📜 Found {len(conversation_history)} messages in conversation history")

            # Create reasoning engine with agentic configuration
            config = ReasoningConfig(
                max_iterations=max(max_tool_calls, 15),  # Ensure at least 15 iterations for thorough work
                max_reasoning_time=400,  # 6.5 minutes for comprehensive investigation
                llm_timeout=45,  # 45 seconds per LLM call for complex reasoning
                tool_timeout=90,  # 90 seconds per tool call for complex operations
            )

            reasoning_engine = ProductionReasoningEngine(self, config)

            # Use production reasoning engine
            async for chunk in reasoning_engine.reason(message, conversation_history, stream):
                if chunk is None or (isinstance(chunk, str) and not chunk.strip()):
                    continue
                yield chunk

            # Signal completion
            yield VercelAIProtocol.finish("stop")

        except Exception as e:
            print(f"❌ Error in reply: {e}")
            import traceback
            traceback.print_exc()
            yield VercelAIProtocol.tool_error(str(e))
            yield VercelAIProtocol.finish("error")




    async def _send_websocket_message(self, websocket, message):
        """Helper to send WebSocket messages safely."""
        try:
            await websocket.send_json(message)
        except Exception as e:
            print(f"WebSocket send error: {e}")

    def _execute_tool_call(self, tool_call) -> str:
        """Execute a single tool call and return formatted result."""
        try:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)

            # Primary path: use tools registry for built-in tools
            tools_registry = get_tools_registry()
            if function_name in tools_registry:
                result = tools_registry[function_name](self, **function_args)
            # Add MCP server
            elif function_name == "add_mcp_server":
                success = self._add_mcp_server_sync(function_args)
                result = json.dumps({"success": success, "message": "MCP server added" if success else "Failed to add MCP server"})
            # MCP tool call - use FastMCP
            else:
                # Assume it's an MCP tool and try to call it
                result = self._call_mcp_tool_sync(function_name, function_args)

            return result

        except Exception as e:
            return json.dumps({"error": f"Tool execution failed: {str(e)}"})

    def _add_mcp_server_sync(self, args: dict) -> bool:
        """Synchronously add an MCP server."""
        try:
            name = args.get("name", "")
            transport = args.get("transport", "stdio")

            server_config = {"transport": transport}

            # Add transport-specific configuration
            if transport == "stdio":
                if "command" in args:
                    server_config["command"] = args["command"]
                if "args" in args:
                    server_config["args"] = args["args"]
                if "env" in args:
                    server_config["env"] = args["env"]
            elif transport in ["http", "sse"]:
                if "url" in args:
                    server_config["url"] = args["url"]
                if "headers" in args:
                    server_config["headers"] = args["headers"]

            if "description" in args:
                server_config["description"] = args["description"]

            return add_mcp_server(name, server_config, self.project_id)
        except Exception as e:
            print(f"❌ Error adding MCP server: {e}")
            return False

    def _call_mcp_tool_sync(self, tool_name: str, args: dict) -> str:
        """Synchronously call an MCP tool."""
        try:
            import asyncio
            import concurrent.futures

            def run_mcp_call_in_thread():
                """Run the MCP call in a separate thread with its own event loop."""
                return asyncio.run(call_mcp_tool(tool_name, args, self.project_id))

            # Always run in a separate thread to avoid event loop conflicts
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_mcp_call_in_thread)
                result = future.result(timeout=30)  # 30 second timeout for MCP calls
                return result
        except Exception as e:
            return json.dumps({"error": f"MCP tool call failed: {str(e)}"})


# EOF
