from fastmcp import FastMCP
import torch
import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Any, Union
import json
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize MCP server
mcp = FastMCP("Chronos")

# Global pipeline storage
pipelines = {}

@mcp.tool
def load_model(
    model_name: str = "amazon/chronos-t5-small",
    device: str = "cpu",
    torch_dtype: str = "bfloat16"
) -> Dict[str, Any]:
    """
    Load a Chronos forecasting model.
    
    Args:
        model_name: HuggingFace model name (e.g., "amazon/chronos-t5-small", "amazon/chronos-bolt-small")
        device: Device to load model on ("cpu", "cuda", "mps")
        torch_dtype: Torch data type ("bfloat16", "float32")
    
    Returns:
        Dictionary with model info and status
    """
    try:
        from chronos import BaseChronosPipeline
        
        # Convert dtype string to torch dtype
        dtype_map = {"bfloat16": torch.bfloat16, "float32": torch.float32}
        torch_dtype = dtype_map.get(torch_dtype, torch.bfloat16)
        
        logger.info(f"Loading model: {model_name}")
        pipeline = BaseChronosPipeline.from_pretrained(
            model_name,
            device_map=device,
            torch_dtype=torch_dtype,
        )
        
        # Store pipeline with model name as key
        pipelines[model_name] = pipeline
        
        return {
            "status": "success",
            "model_name": model_name,
            "device": device,
            "torch_dtype": torch_dtype,
            "message": f"Model {model_name} loaded successfully"
        }
        
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return {
            "status": "error",
            "model_name": model_name,
            "error": str(e)
        }

@mcp.tool
def forecast_quantiles(
    time_series: List[float],
    prediction_length: int = 12,
    quantile_levels: List[float] = [0.1, 0.5, 0.9],
    model_name: str = "amazon/chronos-t5-small",
    num_samples: int = 100
) -> Dict[str, Any]:
    """
    Generate quantile forecasts for a time series.
    
    Args:
        time_series: List of historical time series values
        prediction_length: Number of future time steps to predict
        quantile_levels: List of quantile levels to compute (e.g., [0.1, 0.5, 0.9])
        model_name: Name of the loaded model to use
        num_samples: Number of samples for probabilistic forecasting
    
    Returns:
        Dictionary containing quantile forecasts and mean predictions
    """
    try:
        if model_name not in pipelines:
            return {
                "status": "error",
                "error": f"Model {model_name} not loaded. Please load it first using load_model."
            }
        
        pipeline = pipelines[model_name]
        
        # Convert time series to tensor
        context = torch.tensor(time_series, dtype=torch.float32)
        
        logger.info(f"Generating forecasts for {len(time_series)} historical points, predicting {prediction_length} future steps")
        
        # Generate quantile forecasts
        # Check if this is a Chronos-Bolt model (doesn't support num_samples)
        if hasattr(pipeline, 'model') and hasattr(pipeline.model, 'config') and 'chronos_config' in pipeline.model.config.__dict__:
            # Chronos-Bolt model - don't pass num_samples
            quantiles, mean = pipeline.predict_quantiles(
                context=context,
                prediction_length=prediction_length,
                quantile_levels=quantile_levels
            )
        else:
            # Regular Chronos model - pass num_samples
            quantiles, mean = pipeline.predict_quantiles(
                context=context,
                prediction_length=prediction_length,
                quantile_levels=quantile_levels,
                num_samples=num_samples
            )
        
        # Convert to lists for JSON serialization
        quantiles_list = quantiles.numpy().tolist()
        mean_list = mean.numpy().tolist()
        
        return {
            "status": "success",
            "quantiles": quantiles_list,
            "mean": mean_list,
            "quantile_levels": quantile_levels,
            "prediction_length": prediction_length,
            "historical_length": len(time_series)
        }
        
    except Exception as e:
        logger.error(f"Error generating forecasts: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@mcp.tool
def forecast_samples(
    time_series: List[float],
    prediction_length: int = 12,
    model_name: str = "amazon/chronos-t5-small",
    num_samples: int = 100,
    temperature: float = 1.0,
    top_k: int = 50,
    top_p: float = 1.0
) -> Dict[str, Any]:
    """
    Generate sample forecasts for a time series.
    
    Args:
        time_series: List of historical time series values
        prediction_length: Number of future time steps to predict
        model_name: Name of the loaded model to use
        num_samples: Number of sample trajectories to generate
        temperature: Sampling temperature (higher = more random)
        top_k: Top-k sampling parameter
        top_p: Top-p sampling parameter
    
    Returns:
        Dictionary containing sample forecasts
    """
    try:
        if model_name not in pipelines:
            return {
                "status": "error",
                "error": f"Model {model_name} not loaded. Please load it first using load_model."
            }
        
        pipeline = pipelines[model_name]
        
        # Convert time series to tensor
        context = torch.tensor(time_series, dtype=torch.float32)
        
        logger.info(f"Generating {num_samples} sample forecasts for {len(time_series)} historical points")
        
        # Generate sample forecasts
        # Check if this is a Chronos-Bolt model (doesn't support sampling parameters)
        if hasattr(pipeline, 'model') and hasattr(pipeline.model, 'config') and 'chronos_config' in pipeline.model.config.__dict__:
            # Chronos-Bolt model - only use basic predict
            samples = pipeline.predict(
                context=context,
                prediction_length=prediction_length
            )
        else:
            # Regular Chronos model - use all sampling parameters
            samples = pipeline.predict(
                context=context,
                prediction_length=prediction_length,
                num_samples=num_samples,
                temperature=temperature,
                top_k=top_k,
                top_p=top_p
            )
        
        # Convert to list for JSON serialization
        samples_list = samples.numpy().tolist()
        
        return {
            "status": "success",
            "samples": samples_list,
            "num_samples": num_samples,
            "prediction_length": prediction_length,
            "historical_length": len(time_series)
        }
        
    except Exception as e:
        logger.error(f"Error generating sample forecasts: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@mcp.tool
def extract_embeddings(
    time_series: List[float],
    model_name: str = "amazon/chronos-t5-small"
) -> Dict[str, Any]:
    """
    Extract encoder embeddings from a Chronos model for a time series.
    
    Args:
        time_series: List of historical time series values
        model_name: Name of the loaded model to use
    
    Returns:
        Dictionary containing embeddings and tokenizer state
    """
    try:
        if model_name not in pipelines:
            return {
                "status": "error",
                "error": f"Model {model_name} not loaded. Please load it first using load_model."
            }
        
        pipeline = pipelines[model_name]
        
        # Check if pipeline has embed method (only available for Chronos models, not Chronos-Bolt)
        if not hasattr(pipeline, 'embed'):
            return {
                "status": "error",
                "error": f"Model {model_name} does not support embedding extraction. Only Chronos models (not Chronos-Bolt) support this feature."
            }
        
        # Convert time series to tensor
        context = torch.tensor(time_series, dtype=torch.float32)
        
        logger.info(f"Extracting embeddings for {len(time_series)} historical points")
        
        # Extract embeddings
        embeddings, tokenizer_state = pipeline.embed(context)
        
        # Convert to lists for JSON serialization
        embeddings_list = embeddings.numpy().tolist()
        
        return {
            "status": "success",
            "embeddings": embeddings_list,
            "embedding_shape": list(embeddings.shape),
            "historical_length": len(time_series)
        }
        
    except Exception as e:
        logger.error(f"Error extracting embeddings: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@mcp.tool
def list_available_models() -> Dict[str, Any]:
    """
    List available Chronos models with their specifications.
    
    Returns:
        Dictionary containing available models and their details
    """
    models = {
        "chronos_t5_models": {
            "amazon/chronos-t5-tiny": {"parameters": "8M", "context_length": 512, "prediction_length": 64},
            "amazon/chronos-t5-mini": {"parameters": "20M", "context_length": 512, "prediction_length": 64},
            "amazon/chronos-t5-small": {"parameters": "46M", "context_length": 512, "prediction_length": 64},
            "amazon/chronos-t5-base": {"parameters": "200M", "context_length": 512, "prediction_length": 64},
            "amazon/chronos-t5-large": {"parameters": "710M", "context_length": 512, "prediction_length": 64}
        },
        "chronos_bolt_models": {
            "amazon/chronos-bolt-tiny": {"parameters": "9M", "context_length": 512, "prediction_length": 64},
            "amazon/chronos-bolt-mini": {"parameters": "21M", "context_length": 512, "prediction_length": 64},
            "amazon/chronos-bolt-small": {"parameters": "48M", "context_length": 512, "prediction_length": 64},
            "amazon/chronos-bolt-base": {"parameters": "205M", "context_length": 512, "prediction_length": 64}
        }
    }
    
    return {
        "status": "success",
        "available_models": models,
        "note": "Chronos-Bolt models are more efficient (5% lower error, up to 250x faster, 20x more memory efficient) than corresponding Chronos models"
    }

@mcp.tool
def get_model_info(model_name: str) -> Dict[str, Any]:
    """
    Get information about a specific loaded model.
    
    Args:
        model_name: Name of the model to get info for
    
    Returns:
        Dictionary containing model information
    """
    if model_name not in pipelines:
        return {
            "status": "error",
            "error": f"Model {model_name} not loaded"
        }
    
    pipeline = pipelines[model_name]
    
    info = {
        "status": "success",
        "model_name": model_name,
        "model_type": type(pipeline).__name__,
        "device": str(next(pipeline.inner_model.parameters()).device),
        "dtype": str(next(pipeline.inner_model.parameters()).dtype),
        "parameters": sum(p.numel() for p in pipeline.inner_model.parameters()),
        "trainable_parameters": sum(p.numel() for p in pipeline.inner_model.parameters() if p.requires_grad)
    }
    
    return info

@mcp.tool
def unload_model(model_name: str) -> Dict[str, Any]:
    """
    Unload a model from memory.
    
    Args:
        model_name: Name of the model to unload
    
    Returns:
        Dictionary with unload status
    """
    if model_name in pipelines:
        del pipelines[model_name]
        return {
            "status": "success",
            "message": f"Model {model_name} unloaded successfully"
        }
    else:
        return {
            "status": "error",
            "error": f"Model {model_name} not found in loaded models"
        }

@mcp.tool
def list_loaded_models() -> Dict[str, Any]:
    """
    List all currently loaded models.
    
    Returns:
        Dictionary containing list of loaded models
    """
    return {
        "status": "success",
        "loaded_models": list(pipelines.keys()),
        "count": len(pipelines)
    }

if __name__ == "__main__":
    mcp.run(transport="http", port=5000)
