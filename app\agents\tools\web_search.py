from typing import Any
import os
import json
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import mimetypes

def run(agent: Any, query: str, is_url: bool = False, download: bool = False) -> str:
    """
    Enhanced web search and download tool.

    Args:
        query: Search query or URL
        is_url: Whether query is a direct URL to fetch
        download: Whether to download the content to project files
    """
    try:
        if is_url:
            return _fetch_url(agent, query, download)
        else:
            return _search_web(query)
    except Exception as e:
        return f"❌ Error: {str(e)}"

def _fetch_url(agent: Any, url: str, download: bool = False) -> str:
    """Fetch content from a URL with optional download."""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }

    resp = requests.get(url, headers=headers, timeout=30)
    resp.raise_for_status()

    content_type = resp.headers.get('content-type', '').lower()

    # Handle different content types
    if 'text/html' in content_type:
        return _process_html(agent, url, resp.content, download)
    elif any(ct in content_type for ct in ['application/json', 'text/json']):
        return _process_json(agent, url, resp.text, download)
    elif 'text/' in content_type:
        return _process_text(agent, url, resp.text, download)
    else:
        # Binary content (images, PDFs, etc.)
        return _process_binary(agent, url, resp.content, content_type, download)

def _process_html(agent: Any, url: str, content: bytes, download: bool) -> str:
    """Process HTML content."""
    soup = BeautifulSoup(content, "html.parser")

    # Remove scripts and styles
    for script in soup(["script", "style", "nav", "footer", "header"]):
        script.decompose()

    # Extract clean text
    text = "\n".join(line.strip() for line in soup.get_text().splitlines() if line.strip())
    title = soup.title.string if soup.title else "No title"

    result = {
        "🌐 URL": url,
        "📄 Title": title,
        "📝 Content": text[:3000] + ("..." if len(text) > 3000 else ""),
        "📏 Full Length": f"{len(text)} characters"
    }

    if download:
        filename = _save_content(agent, url, text, "html")
        result["💾 Saved"] = filename

    return _format_result(result)

def _process_json(agent: Any, url: str, content: str, download: bool) -> str:
    """Process JSON content."""
    try:
        data = json.loads(content)
        formatted = json.dumps(data, indent=2)

        result = {
            "🌐 URL": url,
            "📊 Type": "JSON Data",
            "📝 Content": formatted[:2000] + ("..." if len(formatted) > 2000 else ""),
            "📏 Size": f"{len(content)} characters"
        }

        if download:
            filename = _save_content(agent, url, formatted, "json")
            result["💾 Saved"] = filename

        return _format_result(result)
    except json.JSONDecodeError:
        return f"❌ Invalid JSON content from {url}"

def _process_text(agent: Any, url: str, content: str, download: bool) -> str:
    """Process plain text content."""
    result = {
        "🌐 URL": url,
        "📄 Type": "Text Content",
        "📝 Content": content[:2000] + ("..." if len(content) > 2000 else ""),
        "📏 Size": f"{len(content)} characters"
    }

    if download:
        filename = _save_content(agent, url, content, "txt")
        result["💾 Saved"] = filename

    return _format_result(result)

def _process_binary(agent: Any, url: str, content: bytes, content_type: str, download: bool) -> str:
    """Process binary content."""
    size_mb = len(content) / (1024 * 1024)

    result = {
        "🌐 URL": url,
        "📦 Type": f"Binary ({content_type})",
        "📏 Size": f"{size_mb:.2f} MB ({len(content)} bytes)",
        "💡 Note": "Binary content - use download=True to save"
    }

    if download:
        filename = _save_binary(agent, url, content, content_type)
        result["💾 Saved"] = filename

    return _format_result(result)

def _save_content(agent: Any, url: str, content: str, extension: str) -> str:
    """Save text content to project input directory."""
    # Create safe filename from URL
    parsed = urlparse(url)
    base_name = parsed.netloc.replace('.', '_') + parsed.path.replace('/', '_')
    base_name = ''.join(c for c in base_name if c.isalnum() or c in '_-')[:50]

    if not base_name:
        base_name = "downloaded_content"

    filename = f"{base_name}.{extension}"

    # Save to input directory
    project_root = f"projects/{agent.project_id}" if agent.project_id else "."
    input_dir = f"{project_root}/input"
    os.makedirs(input_dir, exist_ok=True)

    filepath = os.path.join(input_dir, filename)

    # Handle duplicate filenames
    counter = 1
    while os.path.exists(filepath):
        name_part = f"{base_name}_{counter}"
        filepath = os.path.join(input_dir, f"{name_part}.{extension}")
        filename = f"{name_part}.{extension}"
        counter += 1

    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    return filename

def _save_binary(agent: Any, url: str, content: bytes, content_type: str) -> str:
    """Save binary content to project input directory."""
    # Determine extension from content type
    extension = mimetypes.guess_extension(content_type) or '.bin'

    # Create safe filename from URL
    parsed = urlparse(url)
    base_name = parsed.netloc.replace('.', '_') + parsed.path.replace('/', '_')
    base_name = ''.join(c for c in base_name if c.isalnum() or c in '_-')[:50]

    if not base_name:
        base_name = "downloaded_file"

    filename = f"{base_name}{extension}"

    # Save to input directory
    project_root = f"projects/{agent.project_id}" if agent.project_id else "."
    input_dir = f"{project_root}/input"
    os.makedirs(input_dir, exist_ok=True)

    filepath = os.path.join(input_dir, filename)

    # Handle duplicate filenames
    counter = 1
    while os.path.exists(filepath):
        name_part = f"{base_name}_{counter}"
        filepath = os.path.join(input_dir, f"{name_part}{extension}")
        filename = f"{name_part}{extension}"
        counter += 1

    with open(filepath, 'wb') as f:
        f.write(content)

    return filename

def _search_web(query: str) -> str:
    """Perform web search."""
    search_url = f"https://duckduckgo.com/html/?q={query}"
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

    resp = requests.get(search_url, headers=headers, timeout=15)
    soup = BeautifulSoup(resp.content, "html.parser")

    results = []
    for result in soup.find_all("div", class_="result")[:8]:  # Get more results
        title_elem = result.find("a", class_="result__a")
        snippet_elem = result.find("a", class_="result__snippet")
        if title_elem:
            results.append({
                "title": title_elem.get_text().strip(),
                "url": title_elem.get("href", ""),
                "snippet": snippet_elem.get_text().strip() if snippet_elem else "",
            })

    if not results:
        return f"🔍 No results found for: {query}"

    output = [f"🔍 Search Results for: {query}\n"]
    for i, result in enumerate(results, 1):
        output.append(f"{i}. 📄 {result['title']}")
        output.append(f"   🔗 {result['url']}")
        if result['snippet']:
            output.append(f"   📝 {result['snippet']}")
        output.append("")

    output.append("💡 To fetch full content: use is_url=True with any URL above")
    output.append("💾 To download content: add download=True")

    return "\n".join(output)

def _format_result(data: dict) -> str:
    """Format result data in a clean, readable way."""
    lines = []
    for key, value in data.items():
        lines.append(f"{key}: {value}")
    return "\n".join(lines)

