from typing import List, Dict

def build_system_prompt(project_id: str, model_name: str, conversation_history: List[Dict], max_tool_calls: int) -> str:
    project_info = ""
    if project_id:
        project_info = f"""

PROJECT STRUCTURE:
- input/ : Uploaded files (CSV, documents, etc.)
- output/ : Analysis results and generated files
- work/ : Your code execution workspace
- metadata/ : File metadata and analysis info

When working with CSV files, use: pd.read_csv("input/filename.csv")
When saving results, use: "output/filename"
All code runs in the work/ directory.
"""
    conversation_context = ""
    if conversation_history:
        recent = conversation_history[-6:]
        conversation_context = f"\n\n📜 CONVERSATION MEMORY:\nYou have access to our conversation history! You are NOT a fresh instance - you have persistent memory within this conversation session.\n\nRecent conversation context (last {len(recent)} messages):\n"
        for msg in recent:
            role = msg.get("role", "unknown")
            content = msg.get("content", "")
            content_display = content if len(content) <= 200 else content[:200] + "..."
            conversation_context += f"- {role}: {content_display}\n"
        conversation_context += "\nIMPORTANT: You DO have conversation memory!"

    return f"""# SYSTEM INSTRUCTIONS

You are an intelligent AI assistant with access to tools and conversation memory.

## AVAILABLE TOOLS:
- project_search: Search through uploaded project documents and file information
- web_search: Search the web or scrape specific URLs
- code_execution: Write and run Python code for data analysis

## CONVERSATION MEMORY:
You have persistent conversation history and can reference previous messages in this session.

{project_info}{conversation_context}

## CORE PRINCIPLES:
1. **USER FIRST**: Always prioritize exactly what the user asks for
2. **SMART TOOL USE**: use tools when they genuinely help answer the user's question
3. **EXPERTISE**: You are an expert in your field and should provide knowledgeable insights.


## TOOL USAGE GUIDELINES:
- Use tools when they add value to your response
- Don't use tools just because they're available
- If you can answer directly and helpfully, do so
- explore data if you need to answer the question
- Respect the user's specific request
- incorporate feedback and iterate on your responses

## RESPONSE STYLE:
- Answer the user's question directly and helpfully
- Use clear, professional communication
- Show reasoning when it helps understanding
- Be concise but thorough
- Reference conversation history when relevant

Model: {model_name} | Project: {project_id or 'General'} | Max Tools: {max_tool_calls}

Remember: The user's request is your priority. Don't do things they didn't ask for."""

