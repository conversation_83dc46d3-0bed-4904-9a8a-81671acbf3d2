from typing import List, Dict

def build_system_prompt(project_id: str, model_name: str, conversation_history: List[Dict], max_tool_calls: int, tools: List[Dict] = None) -> str:
    project_info = ""
    if project_id:
        project_info = f"""

PROJECT STRUCTURE:
- input/ : Uploaded files (CSV, documents, etc.)
- output/ : Analysis results and generated files
- work/ : Your code execution workspace
- metadata/ : File metadata and analysis info

When working with CSV files, use: pd.read_csv("input/filename.csv")
When saving results, use: "output/filename"
All code runs in the work/ directory.
"""
    conversation_context = ""
    if conversation_history:
        recent = conversation_history[-6:]
        conversation_context = f"\n\n📜 CONVERSATION MEMORY:\nYou have access to our conversation history! You are NOT a fresh instance - you have persistent memory within this conversation session.\n\nRecent conversation context (last {len(recent)} messages):\n"
        for msg in recent:
            role = msg.get("role", "unknown")
            content = msg.get("content", "")
            content_display = content if len(content) <= 200 else content[:200] + "..."
            conversation_context += f"- {role}: {content_display}\n"
        conversation_context += "\nIMPORTANT: You DO have conversation memory!"

    # Build dynamic tools list
    tools_list = ""
    if tools:
        tools_list = "## AVAILABLE TOOLS:\n"
        for tool in tools:
            if tool.get("type") == "function" and "function" in tool:
                func = tool["function"]
                name = func.get("name", "unknown")
                desc = func.get("description", "No description")
                tools_list += f"- {name}: {desc}\n"
    else:
        # Fallback to basic tools
        tools_list = """## AVAILABLE TOOLS:
- find_files: Find files in the project directory with fuzzy search
- project_search: Search through uploaded project documents and file information
- web_search: Search the web or scrape specific URLs
- code_execution: Write and run Python code for data analysis"""

    return f"""You are an intelligent AI assistant with access to tools.

{tools_list}

{project_info}{conversation_context}

Use tools when they help answer the user's question. Answer directly and helpfully.

Model: {model_name} | Project: {project_id or 'General'} | Max Tools: {max_tool_calls}"""

