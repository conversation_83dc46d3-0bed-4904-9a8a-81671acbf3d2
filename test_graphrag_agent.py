#!/usr/bin/env python3
"""
Test script to verify GraphRAG agent is working with project_search tool.
"""

import os
from app.agents.BaseAgent import BaseAgent

def test_base_agent():
    """Test if BaseAgent is working properly."""

    print("🤖 Testing BaseAgent functionality...")

    try:
        # Set a dummy OpenAI API key for testing (won't actually call OpenAI)
        if not os.getenv("OPENAI_API_KEY"):
            print("⚠️  No OpenAI API key set - this test will show tool availability only")

        # Create BaseAgent
        agent = BaseAgent(project_id="test")
        
        print("✅ BaseAgent initialized")

        # Check if tools are properly registered
        print(f"🛠️  Available tools: {len(agent.tools)} tools")
        for tool in agent.tools:
            print(f"   - {tool['function']['name']}: {tool['function']['description']}")

        # Test the project_search tool directly
        print("🔍 Testing project_search tool...")
        search_result = agent.project_search("final accuracy", k=3)
        print(f"📄 Search result: {search_result}")

        # Test web search tool
        print("🌐 Testing web_search tool...")
        web_result = agent.web_search("python programming", is_url=False)
        print(f"🔍 Web search result: {web_result[:200]}...")

        # Test code execution tool
        print("💻 Testing code_execution tool...")
        code_result = agent.code_execution("print('Hello from BaseAgent!')")
        print(f"🐍 Code execution result: {code_result}")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_base_agent()
    if success:
        print("\n🎉 BaseAgent test completed!")
    else:
        print("\n💥 BaseAgent test failed!")
