# Graph-RAG Implementation for Kairos Backend

This implementation adds Graph-RAG (Retrieval-Augmented Generation with Knowledge Graphs) capabilities to your existing Kairos backend with minimal changes to your codebase.

## 🏗️ Architecture Overview

The implementation follows your existing manager pattern:

```
ProjectManager
├── FileManager (existing)
├── ConversationManager (existing)
└── AgentManager (enhanced)
    └── GraphRAGAgent (new)
        └── DocumentManager (new)
```

## 📁 Files Added/Modified

### New Files
- `app/managers/DocumentManager.py` - Handles document processing, embeddings, and knowledge graph extraction
- `app/agents/GraphRAGAgent.py` - Extends BaseAgent with Graph-RAG capabilities
- `app/routers/document_router.py` - API endpoints for document processing and search
- `app/db/init_graphrag_db.py` - Database initialization script
- `setup_graphrag.py` - Setup script for easy installation
- `.env.example` - Environment configuration template

### Modified Files
- `app/managers/AgentManager.py` - Enhanced to support GraphRAGAgent
- `app/managers/ProjectManager.py` - Passes project_id to AgentManager
- `app/main.py` - Includes document router
- `requirements.txt` - Added Graph-RAG dependencies

## 🚀 Quick Setup

1. **Run the setup script:**
   ```bash
   python setup_graphrag.py
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. **Start PostgreSQL** (if not already running)

4. **Set OpenAI API key:**
   ```bash
   export OPENAI_API_KEY=your_key_here
   ```

5. **Start the application:**
   ```bash
   uvicorn app.main:app --reload
   ```

## 📊 Database Schema

### PostgreSQL with pgvector
- **Vector embeddings** stored in pgvector collections
- **Knowledge graph triples** in `triples` table:
  ```sql
  CREATE TABLE triples (
      id SERIAL PRIMARY KEY,
      subject TEXT NOT NULL,
      predicate TEXT NOT NULL,
      object TEXT NOT NULL,
      chunk_id TEXT,
      project_id TEXT,
      confidence FLOAT DEFAULT 1.0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
  ```

## 🔧 API Endpoints

### Document Processing
```http
POST /api/projects/{project_id}/documents/upload
Content-Type: multipart/form-data

file: <document_file>
metadata: {"author": "John Doe", "category": "research"}
```

### Vector Search
```http
POST /api/projects/{project_id}/search/vector
Content-Type: application/json

{
  "query": "What is machine learning?",
  "k": 5
}
```

### Knowledge Graph Query
```http
POST /api/projects/{project_id}/search/knowledge-graph
Content-Type: application/json

{
  "subject": "Einstein",
  "predicate": "born",
  "object": null
}
```

### Hybrid Search
```http
POST /api/projects/{project_id}/search/hybrid
Content-Type: application/json

{
  "query": "Einstein's contributions to physics",
  "k": 5
}
```

### Statistics
```http
GET /api/projects/{project_id}/graphrag/stats
```

## 🧠 How It Works

1. **Document Upload**: Documents are chunked using RecursiveCharacterTextSplitter
2. **Embedding Generation**: OpenAI embeddings are generated for each chunk
3. **Vector Storage**: Embeddings stored in PostgreSQL with pgvector
4. **Knowledge Extraction**: spaCy extracts subject-predicate-object triples
5. **Graph Storage**: Triples stored in PostgreSQL for structured queries

## 🔍 Agent Integration

The GraphRAGAgent extends your BaseAgent and provides tools:

```python
# In your conversation flow
project = ProjectManager.from_project_id(project_id)
response = project.talk("What did Einstein discover?")
# Agent automatically uses Graph-RAG capabilities
```

## 🛠️ Tools Available

The GraphRAGAgent provides these tools to the conversation system:

- `vector_search_tool(query, k=5)` - Semantic similarity search
- `knowledge_graph_query_tool(subject, predicate, obj)` - Structured fact lookup
- `hybrid_search_tool(query, k=5)` - Combined vector + knowledge graph search
- `process_document_tool(file_path, metadata)` - Process new documents

## 📈 Usage Examples

### Processing a Document
```python
from app.managers.ProjectManager import ProjectManager

project = ProjectManager.from_project_id("your-project-id")
result = project.agent_manager.process_document(
    "/path/to/document.txt",
    {"category": "research", "author": "John Doe"}
)
```

### Querying Knowledge
```python
# Vector search for contextual information
response = project.talk("Explain quantum mechanics")

# The agent will automatically:
# 1. Perform hybrid search
# 2. Find relevant document chunks
# 3. Extract related knowledge graph facts
# 4. Generate a comprehensive response
```

## 🔧 Configuration

### Environment Variables
```bash
# PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=kairos_graphrag
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# OpenAI
OPENAI_API_KEY=your_key_here
```

### Customization
- **Chunk size**: Modify `chunk_size` in DocumentManager
- **Embedding model**: Change `OpenAIEmbeddings` to other providers
- **Knowledge extraction**: Enhance `extract_triples()` method
- **Search algorithms**: Customize search logic in GraphRAGAgent

## 🚨 Troubleshooting

### Common Issues

1. **PostgreSQL connection failed**
   - Ensure PostgreSQL is running
   - Check connection details in .env
   - Verify pgvector extension is installed

2. **spaCy model not found**
   ```bash
   python -m spacy download en_core_web_sm
   ```

3. **OpenAI API errors**
   - Verify API key is set correctly
   - Check API quota and billing

4. **Import errors**
   - Ensure all dependencies are installed
   - Run `pip install -r requirements.txt`

## 🎯 Benefits

✅ **Minimal codebase changes** - Follows existing patterns
✅ **PostgreSQL-only** - No additional vector databases needed
✅ **Project isolation** - Each project has its own knowledge base
✅ **Hybrid search** - Combines semantic and structured search
✅ **Extensible** - Easy to add new agents and tools
✅ **Git integration** - Document processing tracked in project history

## 🔮 Future Enhancements

- **Multi-modal support** - Images, PDFs, audio
- **Advanced NLP** - Better entity extraction and relation detection
- **Caching layer** - Redis for frequently accessed embeddings
- **Batch processing** - Handle multiple documents efficiently
- **Custom models** - Support for local/private LLMs
