# PostgreSQL Configuration for Graph-RAG
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=kairos_graphrag
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# LLM Provider Configuration
# Options: openai, gemini
# Both providers use OpenAI-compatible APIs for seamless switching
LLM_PROVIDER=gemini

# Embedding Provider Configuration
# Options: openai, google, offline
EMBEDDING_PROVIDER=google

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Google/Gemini API Configuration
# Works with both Gemini LLM and Google embeddings
GOOGLE_API_KEY=your_google_api_key_here

# Application Configuration
DEBUG=True
LOG_LEVEL=INFO
