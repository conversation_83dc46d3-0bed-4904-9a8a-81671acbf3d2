from fastapi import APIRouter, UploadFile, File, HTTPException, Request
from fastapi.responses import StreamingResponse
from app.managers.ProjectManager import ProjectManager
from app.managers.AgentManager import AgentManager
from app.utils.csv_handler import process_csv_simple
from app.utils.fastmcp_client import get_mcp_manager
import tempfile
import os

router = APIRouter()

@router.post("/project/{project_id}/chat")
async def project_chat_stream(project_id: str, request: Request):
    """HTTP streaming endpoint for project chat (Vercel useChat compatible)."""
    data = await request.json()
    messages = data.get("messages", [])
    if not messages:
        raise HTTPException(status_code=400, detail="No messages provided")
    latest_message = messages[-1]
    if latest_message.get("role") != "user":
        raise HTTPException(status_code=400, detail="Last message must be from user")
    message_content = latest_message.get("content", "")
    if isinstance(message_content, list):
        text_parts = [part.get("text", "") for part in message_content if part.get("type") == "text"]
        message = " ".join(text_parts)
    else:
        message = message_content

    project = ProjectManager.from_project_id(project_id, base_dir="./projects")
    if not project:
        raise HTTPException(status_code=404, detail="Project does not exist or is unavailable.")
    base_agent = project.agent_manager.get_agent("base")
    if not base_agent:
        raise HTTPException(status_code=500, detail="Agent not available")

    async def event_stream():
        # Let BaseAgent handle all protocol formatting directly
        async for chunk in project.talk(message, max_tool_calls=25, stream=True):
            if chunk is not None:
                yield chunk

    headers = {
        "X-Vercel-AI-Data-Stream": "v1",
        "Content-Type": "text/plain; charset=utf-8"
    }
    return StreamingResponse(event_stream(), headers=headers, media_type="text/plain")


@router.post("/project/{project_id}/upload")
async def upload_input_file(project_id: str, file: UploadFile = File(...)):
    """Simple upload: CSV/Excel -> metadata extraction + RAG, TXT/MD -> direct RAG"""
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Read file content as bytes
        content_bytes = await file.read()

        # Save file properly to input folder
        file_path = project.file_manager.input_dir / file.filename
        file_path.write_bytes(content_bytes)  # Save as bytes to preserve file integrity
        saved_path = str(file_path)
        print(f"Saved file to {saved_path}")

        # Get file extension
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''

        # Process based on file type
        if file_ext in ['csv', 'xlsx', 'xls']:
            # CSV/Excel: Extract metadata and RAG the metadata
            result = process_csv_simple(saved_path, project_id)
            return {
                "filename": file.filename,
                "saved_path": saved_path,
                "type": "csv_metadata",
                "success": result["success"],
                "message": result.get("message", result.get("error"))
            }

        elif file_ext in ['txt', 'md']:
            # TXT/MD: Direct RAG
            base_agent = project.agent_manager.get_agent("base")
            if base_agent and base_agent.document_manager:
                result = base_agent.document_manager.process_file_upload(
                    content_bytes, file.filename, {"project_id": project_id}
                )
                return {
                    "filename": file.filename,
                    "saved_path": saved_path,
                    "type": "direct_rag",
                    "success": result.get("status") == "success",
                    "message": "File processed and indexed"
                }

        # Other files: just save
        return {
            "filename": file.filename,
            "saved_path": saved_path,
            "type": "saved_only",
            "success": True,
            "message": "File saved to input folder"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.post("/project/{project_id}/search")
async def search_project(project_id: str, query: str):
    """Simple search endpoint for Graph-RAG queries."""
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Use the agent's reply method which will automatically use Graph-RAG
        response = project.agent_manager.generate_response(query)

        return {
            "query": query,
            "response": response,
            "project_id": project_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@router.post("/cache/clear")
async def clear_all_caches():
    """Clear all caches for debugging/management purposes."""
    ProjectManager._instances.clear()
    AgentManager.clear_agent_cache()
    return {"message": "All caches cleared successfully"}

@router.get("/cache/status")
async def get_cache_status():
    """Get current cache status for debugging."""
    return {
        "project_cache_count": len(ProjectManager._instances),
        "agent_cache_count": len(AgentManager._agent_cache),
        "cached_projects": list(ProjectManager._instances.keys()),
        "cached_agents": list(AgentManager._agent_cache.keys())
    }

# MCP Management Endpoints

@router.get("/mcp/servers")
async def list_mcp_servers():
    """List all configured MCP servers."""
    mcp_manager = get_mcp_manager()
    servers = mcp_manager.list_servers()
    server_configs = {}
    for server_name in servers:
        config = mcp_manager.get_server_config(server_name)
        if config:
            server_configs[server_name] = config

    return {
        "servers": servers,
        "configurations": server_configs
    }

@router.post("/mcp/servers")
async def add_mcp_server(server_data: dict):
    """Add a new MCP server."""
    try:
        name = server_data.get("name")
        if not name:
            raise HTTPException(status_code=400, detail="Server name is required")

        # Remove name from config since it's used as key
        server_config = {k: v for k, v in server_data.items() if k != "name"}

        mcp_manager = get_mcp_manager()
        success = mcp_manager.add_server(name, server_config)

        if success:
            # Clear tools cache to pick up new server
            mcp_manager.clear_cache()
            AgentManager.clear_agent_cache()  # Clear agent cache to refresh tools

            return {"success": True, "message": f"MCP server '{name}' added successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to add MCP server")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error adding MCP server: {str(e)}")

@router.delete("/mcp/servers/{server_name}")
async def remove_mcp_server(server_name: str):
    """Remove an MCP server."""
    try:
        mcp_manager = get_mcp_manager()
        success = mcp_manager.remove_server(server_name)

        if success:
            # Clear caches to remove tools from removed server
            mcp_manager.clear_cache()
            AgentManager.clear_agent_cache()

            return {"success": True, "message": f"MCP server '{server_name}' removed successfully"}
        else:
            raise HTTPException(status_code=404, detail=f"MCP server '{server_name}' not found")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error removing MCP server: {str(e)}")

@router.get("/mcp/tools")
async def list_mcp_tools():
    """List all available MCP tools."""
    try:
        mcp_manager = get_mcp_manager()
        tools = await mcp_manager.get_tools()

        return {
            "tools": tools,
            "count": len(tools)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing MCP tools: {str(e)}")

@router.post("/mcp/tools/refresh")
async def refresh_mcp_tools():
    """Refresh MCP tools cache."""
    try:
        mcp_manager = get_mcp_manager()
        mcp_manager.clear_cache()
        AgentManager.clear_agent_cache()

        # Get fresh tools
        tools = await mcp_manager.get_tools()

        return {
            "success": True,
            "message": "MCP tools refreshed",
            "tool_count": len(tools)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error refreshing MCP tools: {str(e)}")