"""
Modern MCP client using FastMCP library.
Supports configuration-based multi-server setup with proper tool discovery.
"""

import json
import os
from typing import Dict, List, Any, Optional
from pathlib import Path

try:
    from fastmcp import Client
except ImportError:
    Client = None
    print("⚠️ FastMCP not installed. Install with: pip install fastmcp")


class FastMCPManager:
    """Modern MCP client manager using FastMCP library."""

    def __init__(self, config_path: str = "config/mcp_config.json", project_id: str = None):
        self.config_path = config_path
        self.project_id = project_id
        self.config = self._load_combined_config()
        self.client = None
        self._tools_cache = None
        
    def _load_combined_config(self) -> Dict[str, Any]:
        """Load combined global + project-level MCP configuration."""
        combined_config = {"mcpServers": {}}

        # 1. Load global config
        global_config = self._load_config_file(self.config_path)
        if global_config.get("mcpServers"):
            combined_config["mcpServers"].update(global_config["mcpServers"])
            print(f"🌍 Loaded {len(global_config['mcpServers'])} global MCP servers")

        # 2. Load project-specific config if project_id is provided
        if self.project_id:
            project_config_path = f"projects/{self.project_id}/mcp_config.json"
            project_config = self._load_config_file(project_config_path)
            if project_config.get("mcpServers"):
                combined_config["mcpServers"].update(project_config["mcpServers"])
                print(f"📁 Loaded {len(project_config['mcpServers'])} project-specific MCP servers for {self.project_id}")

        total_servers = len(combined_config["mcpServers"])
        print(f"🔧 Total MCP servers available: {total_servers}")
        return combined_config

    def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        """Load MCP configuration from a specific JSON file."""
        try:
            config_file = Path(config_path)
            if config_file.exists():
                with open(config_file, 'r') as f:
                    return json.load(f)
            else:
                return {"mcpServers": {}}
        except Exception as e:
            print(f"⚠️ Error loading MCP config from {config_path}: {e}")
            return {"mcpServers": {}}
    
    def _save_config(self):
        """Save current configuration to file."""
        try:
            config_file = Path(self.config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            print(f"💾 Saved MCP config to {self.config_path}")
        except Exception as e:
            print(f"❌ Error saving MCP config: {e}")
    
    def add_server(self, name: str, server_config: Dict[str, Any]) -> bool:
        """Add a new MCP server to the configuration."""
        try:
            self.config.setdefault("mcpServers", {})[name] = server_config
            self._save_config()
            self._tools_cache = None  # Invalidate cache
            print(f"✅ Added MCP server: {name}")
            return True
        except Exception as e:
            print(f"❌ Error adding MCP server {name}: {e}")
            return False
    
    def remove_server(self, name: str) -> bool:
        """Remove an MCP server from the configuration."""
        try:
            if name in self.config.get("mcpServers", {}):
                del self.config["mcpServers"][name]
                self._save_config()
                self._tools_cache = None  # Invalidate cache
                print(f"🗑️ Removed MCP server: {name}")
                return True
            else:
                print(f"⚠️ MCP server not found: {name}")
                return False
        except Exception as e:
            print(f"❌ Error removing MCP server {name}: {e}")
            return False
    
    def list_servers(self) -> List[str]:
        """List all configured MCP servers."""
        return list(self.config.get("mcpServers", {}).keys())
    
    def get_server_config(self, name: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific server."""
        return self.config.get("mcpServers", {}).get(name)
    
    async def get_tools(self) -> List[Dict[str, Any]]:
        """Get all available tools from all configured MCP servers."""
        if not Client:
            print("⚠️ FastMCP not available, returning empty tools list")
            return []
        
        if self._tools_cache is not None:
            return self._tools_cache
        
        tools = []
        
        if not self.config.get("mcpServers"):
            print("📝 No MCP servers configured")
            return tools
        
        try:
            # Create client with multi-server configuration
            client = Client(self.config)
            
            async with client:
                # List all available tools
                server_tools = await client.list_tools()
                
                # Convert to OpenAI-style tool specs
                for tool in server_tools:
                    tool_spec = {
                        "type": "function",
                        "function": {
                            "name": tool.name,
                            "description": tool.description or "MCP tool",
                            "parameters": tool.inputSchema or {
                                "type": "object",
                                "properties": {},
                                "required": []
                            }
                        }
                    }
                    tools.append(tool_spec)
                
                print(f"🔧 Loaded {len(tools)} tools from {len(self.config['mcpServers'])} MCP servers")
                
        except Exception as e:
            print(f"❌ Error loading MCP tools: {e}")
        
        self._tools_cache = tools
        return tools
    
    async def call_tool(self, tool_name: str, args: Dict[str, Any]) -> str:
        """Call an MCP tool and return the result as JSON string."""
        if not Client:
            return json.dumps({"error": "FastMCP not available"})
        
        try:
            client = Client(self.config)
            
            async with client:
                result = await client.call_tool(tool_name, args)
                
                # Convert result to JSON string
                if hasattr(result, 'content'):
                    # Handle MCP result objects - extract text content
                    if hasattr(result.content, '__iter__') and not isinstance(result.content, str):
                        # Handle list of content objects
                        content_text = ""
                        for item in result.content:
                            if hasattr(item, 'text'):
                                content_text += item.text
                            elif hasattr(item, 'content'):
                                content_text += str(item.content)
                            else:
                                content_text += str(item)
                    else:
                        # Handle single content object
                        if hasattr(result.content, 'text'):
                            content_text = result.content.text
                        else:
                            content_text = str(result.content)

                    return json.dumps({
                        "success": True,
                        "result": content_text,
                        "tool": tool_name,
                        "args": args
                    })
                else:
                    # Handle direct results
                    return json.dumps({
                        "success": True,
                        "result": str(result),
                        "tool": tool_name,
                        "args": args
                    })
                    
        except Exception as e:
            return json.dumps({
                "error": f"MCP tool call failed: {str(e)}",
                "tool": tool_name,
                "args": args
            })
    
    def clear_cache(self):
        """Clear the tools cache to force refresh."""
        self._tools_cache = None
        print("🧹 Cleared MCP tools cache")


# Global and project-specific instances
_global_mcp_manager = None
_project_mcp_managers = {}

def get_mcp_manager(project_id: str = None) -> FastMCPManager:
    """Get MCP manager instance - global or project-specific."""
    if project_id:
        # Return project-specific manager
        if project_id not in _project_mcp_managers:
            print(f"🔧 Creating project-specific MCP manager for {project_id}")
            _project_mcp_managers[project_id] = FastMCPManager(project_id=project_id)
        return _project_mcp_managers[project_id]
    else:
        # Return global manager
        global _global_mcp_manager
        if _global_mcp_manager is None:
            print("🌍 Creating global MCP manager")
            _global_mcp_manager = FastMCPManager()
        return _global_mcp_manager


# Convenience functions for backward compatibility
async def get_mcp_tools(project_id: str = None) -> List[Dict[str, Any]]:
    """Get all MCP tools from configured servers."""
    manager = get_mcp_manager(project_id)
    return await manager.get_tools()


async def call_mcp_tool(tool_name: str, args: Dict[str, Any], project_id: str = None) -> str:
    """Call an MCP tool and return JSON result."""
    manager = get_mcp_manager(project_id)
    return await manager.call_tool(tool_name, args)


def add_mcp_server(name: str, server_config: Dict[str, Any], project_id: str = None) -> bool:
    """Add a new MCP server."""
    manager = get_mcp_manager(project_id)
    return manager.add_server(name, server_config)


def remove_mcp_server(name: str, project_id: str = None) -> bool:
    """Remove an MCP server."""
    manager = get_mcp_manager(project_id)
    return manager.remove_server(name)


def list_mcp_servers(project_id: str = None) -> List[str]:
    """List all configured MCP servers."""
    manager = get_mcp_manager(project_id)
    return manager.list_servers()


