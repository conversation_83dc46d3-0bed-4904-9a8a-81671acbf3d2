import json
from typing import Dict, Any, Union


class VercelAIProtocol:
    """
    Helper class to format responses according to Vercel AI SDK data stream protocol.
    
    Protocol format:
    - 0:"text content" - Text content (quoted string)
    - 3:"error message" - Error message (quoted string)
    - 9:{tool_call} - Tool call (JSON object)
    - a:{tool_result} - Tool result (JSON object)
    - b:{tool_start} - Tool start (JSON object)
    - c:{tool_delta} - Tool delta (JSON object)
    - d:{finish_data} - Finish data (JSON object)
    - e:{event_data} - Event data (JSON object)
    - g:"reasoning" - Reasoning part (quoted string)
    - i:{redacted_data} - Redacted reasoning (JSON object)
    - j:{signature} - Reasoning signature (JSON object)
    """
    
    @staticmethod
    def text(content: str) -> str:
        """Format text content."""
        # Properly escape the content for JSON
        escaped_content = json.dumps(content)
        return f'0:{escaped_content}\n'
    
    @staticmethod
    def error(message: str) -> str:
        """Format error message."""
        # Properly escape the message for JSON
        escaped_message = json.dumps(message)
        return f'3:{escaped_message}\n'
    
    @staticmethod
    def tool_error(message: str) -> str:
        """Format tool execution error."""
        # Properly escape the message for JSON
        escaped_message = json.dumps(message)
        return f'3:{escaped_message}\n'
    
    @staticmethod
    def reasoning(content: str) -> str:
        """Format reasoning part."""
        # Properly escape the content for JSON
        escaped_content = json.dumps(content)
        return f'g:{escaped_content}\n'
    
    @staticmethod
    def redacted_reasoning(data: str) -> str:
        """Format redacted reasoning part."""
        return f'i:{json.dumps({"data": data})}\n'
    
    @staticmethod
    def reasoning_signature(signature: str) -> str:
        """Format reasoning signature part."""
        return f'j:{json.dumps({"signature": signature})}\n'
    
    @staticmethod
    def tool_call(tool_call_id: str, tool_name: str, args: Dict[str, Any]) -> str:
        """Format tool call."""
        data = {
            "toolCallId": tool_call_id,
            "toolName": tool_name,
            "args": args
        }
        return f'9:{json.dumps(data)}\n'
    
    @staticmethod
    def tool_result(tool_call_id: str, result: str) -> str:
        """Format tool result."""
        data = {
            "toolCallId": tool_call_id,
            "result": result
        }
        return f'a:{json.dumps(data)}\n'
    
    @staticmethod
    def tool_start(tool_call_id: str, tool_name: str) -> str:
        """Format tool start notification."""
        data = {
            "toolCallId": tool_call_id,
            "toolName": tool_name
        }
        return f'b:{json.dumps(data)}\n'
    
    @staticmethod
    def tool_delta(tool_call_id: str, args_delta: str) -> str:
        """Format tool arguments delta."""
        data = {
            "toolCallId": tool_call_id,
            "argsTextDelta": args_delta
        }
        return f'c:{json.dumps(data)}\n'
    
    @staticmethod
    def finish(finish_reason: str = "stop", usage: Dict[str, int] = None) -> str:
        """Format completion message."""
        data = {
            "finishReason": finish_reason,
            "usage": usage or {"promptTokens": 0, "completionTokens": 0}
        }
        return f'd:{json.dumps(data)}\n'
        
    @staticmethod
    def event(event_data: Dict[str, Any]) -> str:
        """Format event data."""
        return f'e:{json.dumps(event_data)}\n'
    