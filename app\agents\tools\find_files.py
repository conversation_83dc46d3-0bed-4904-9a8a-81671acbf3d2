from typing import Any
from .project_file_utils import get_project_file_manager

def run(agent: Any, pattern: str = "*") -> str:
    """
    Find files in the project and return paths that work with code_execution.
    Returns paths relative to the work directory for easy use in code.
    """
    try:
        # Use the new project file manager
        file_manager = get_project_file_manager(agent)
        return file_manager.format_find_results(pattern)

    except Exception as e:
        return f"Error searching for files: {str(e)}"

